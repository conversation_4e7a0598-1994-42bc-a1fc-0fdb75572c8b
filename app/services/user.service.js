import request from '../utils/request';
import env from 'env';

export function getUserBalance() {
  const requestURL = `${env.API_URL}/users/me`;

  return request(requestURL, { method: 'GET' });
}

// export function getPromotions() {
//   const requestURL = `${env.API_URL}/users/promotions`;
//
//   return request(requestURL, { method: 'GET' });
// }

export function changePassword(data) {
  const requestURL = `${env.API_URL}/users/reset-password`;

  return request(requestURL, { method: 'PUT', body: data });
}

export function placeOrderProxy(data) {
  const requestURL = `${env.API_URL}/client/transactions/place-order`;

  return request(requestURL, { method: 'POST', body: data });
}

export function placeOrderV2Proxy(data) {
  const requestURL = `${env.API_URL}/client/orders/place-order`;

  return request(requestURL, { method: 'POST', body: data });
}

export function redeemCode(promotionCode, amount) {
  const requestURL = `${env.API_URL}/client/promotions/redeem-code?promotionCode=${promotionCode}&totalAmount=${amount}`;

  return request(requestURL, { method: 'GET' });
}

export function changeIp(data) {
  const requestURL = `${env.API_URL}/client/proxies/change-ip`;

  return request(requestURL, { method: 'POST', body: data });
}

export function changeLocation(data) {
  const requestURL = `${env.API_URL}/client/proxies/change-location`;

  return request(requestURL, { method: 'POST', body: data });
}

export function extendLicense(data) {
  const requestURL = `${env.API_URL}/client/proxies/extend-license`;

  return request(requestURL, { method: 'POST', body: data });
}

export function updateAuthenticate(data) {
  const requestURL = `${env.API_URL}/client/proxies/update-authenticate`;

  return request(requestURL, { method: 'POST', body: data });
}

export function getLicenses(data) {
  const requestURL = `${env.API_URL}/client/licenses/list`;

  return request(requestURL, { method: 'POST', body: data });
}


export function getTransactions(data) {
  const requestURL = `${env.API_URL}/client/transactions/list`;

  return request(requestURL, { method: 'POST', body: data });
}

export function verifyAcc(code) {
  const requestURL = `${env.API_URL}/users/verify/${code}`;

  return request(requestURL, { method: 'GET' });
}

export function updateRotationTime(data) {
  const requestURL = `${env.API_URL}/client/licenses/change-rotation-time`;

  return request(requestURL, { method: 'POST', body: data });
}

export function changeReminder() {
  const requestURL = `${env.API_URL}/users/change-reminder`;

  return request(requestURL, { method: 'GET' });
}

export function getAllLocations() {
  const requestURL = `${env.API_URL}/locations/list`;

  return request(requestURL, { method: 'GET' });
}

export function rebootDevice(data) {
  const requestURL = `${env.API_URL}/client/proxies/reboot-device`;

  return request(requestURL, { method: 'POST', body: data });
}

export function changeVpn(data) {
  const requestURL = `${env.API_URL}/client/vpn/change`;

  return request(requestURL, { method: 'POST', body: data });
}

export function downloadVpn(licenseUuid) {
  const requestURL = `${env.API_URL}/client/vpn/download?licenseUuid=${licenseUuid}`;

  return request(requestURL, { method: 'GET', body: data });
}

export function getPromotionQuantity(data) {
  const requestURL = `${env.API_URL}/client/promotions/quantity`;

  return request(requestURL, { method: 'POST', body: data });
}

export function discountAmount(promotionId, amount) {
  const requestURL = `${env.API_URL}/client/promotions/discount?id=${promotionId}&totalAmount=${amount}`;

  return request(requestURL, { method: 'GET' });
}

export function toggleAutoRenewal(data) {
  const requestURL = `${env.API_URL}/client/licenses/toggle-auto-renewal`;

  return request(requestURL, { method: 'POST', body: data });
}

export function cancelVpn(data) {
  const requestURL = `${env.API_URL}/client/licenses/cancel-vpn`;

  return request(requestURL, { method: 'POST', body: data });
}

export function purchaseVpn(data) {
  const requestURL = `${env.API_URL}/client/licenses/purchase-vpn`;

  return request(requestURL, { method: 'POST', body: data });
}

export function expressExtendLicense(data) {
  const requestURL = `${env.API_URL}/client/licenses/express-extend`;

  return request(requestURL, { method: 'POST', body: data });
}

export function updateTcpOs(data) {
  const requestURL = `${env.API_URL}/client/licenses/update-tcp-os`;

  return request(requestURL, { method: 'POST', body: data });
}
