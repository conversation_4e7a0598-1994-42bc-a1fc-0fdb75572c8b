import React from 'react';
import PropTypes from 'prop-types';
import StyledExternalNavLink from './StyledExternalNavLink';
import ReactTooltip from 'react-tooltip';

// eslint-disable-next-line react/prefer-stateless-function
class ExternalNavLink extends React.Component {
  handleClick = (e) => {
    e.preventDefault();
    window.open(this.props.to, '_blank', 'noopener,noreferrer');
    
    // Call onClick if provided (for mobile collapse)
    if (this.props.onClick) {
      this.props.onClick(e);
    }
  }

  render() {
    let text;
    if (this.props.children) {
      text = this.props.children;
    } else if (this.props.text) {
      text = this.props.text;
    }

    return (
      <div>
        <StyledExternalNavLink
          className={` ${this.props.isCollapse ? 'is-collapse' : ''}`}
          href={this.props.to}
          onClick={this.handleClick}
          data-for={this.props.to}
          data-tip
        >
          <img
            src={this.props.icon}
            height={this.props.height}
            width={this.props.width}
            alt={'icon'}
          />
          {this.props.isCollapse ? (
            <ReactTooltip
              place="right"
              type="light"
              effect="solid"
              className="custom-tooltip"
              id={this.props.to}
            >
              <span>{text}</span>
            </ReactTooltip>
          ) : null}

          <span className={'label ml-2'}>{text}</span>
        </StyledExternalNavLink>
      </div>
    );
  }
}

ExternalNavLink.propTypes = {
  icon: PropTypes.string.isRequired,
  width: PropTypes.number,
  height: PropTypes.number,
  text: PropTypes.string,
  to: PropTypes.string.isRequired,
  isCollapse: PropTypes.bool,
  onClick: PropTypes.func,
};

export default ExternalNavLink;
