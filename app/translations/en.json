﻿{
  "app.responseCode.401": "Account out of session.",

  "app.containers.AuthPage.FormLogin.FormLogin.title": "Login",
  "app.containers.AuthPage.FormLogin.FormLogin.submit": "Login",
  "app.containers.AuthPage.FormLogin.FormLogin.remember_me": "Remember login",
  "app.containers.AuthPage.FormLogin.FormLogin.forgot_password": "Forgot password",
  "app.containers.AuthPage.FormLogin.FormLogin.have_no_account": "Don't have an account yet?",
  "app.containers.AuthPage.FormLogin.FormLogin.register": "Register",
  "app.containers.AuthPage.FormLogin.phoneInvalidError": "Please enter a valid phone number.",
  "app.containers.AuthPage.FormLogin.phoneRequiredError": "Please enter phone number.",
  "app.containers.AuthPage.FormLogin.phoneMaxLengthError": "Please enter less than 15 characters",
  "app.containers.AuthPage.FormLogin.passwordRequiredError": "Please enter a password.",
  "app.containers.AuthPage.FormLogin.passwordMaxLengthError": "Please enter password less than 15 characters.",
  "app.containers.AuthPage.FormLogin.passwordMinLengthError": "Please enter more than 8 characters.",
  "app.containers.AuthPage.FormLogin.changePassword": "Change Password",
  "app.containers.AuthPage.FormLogin.reset_password": "Reset password",
  "app.containers.AuthPage.FormLogin.see_location": "See information about proxy status and availability",
  "app.containers.AuthPage.FormLogin.click_here": "Click here",

  "app.containers.AuthPage.info1": "AAAProxy.io - Best Mobile Proxy",
  "app.containers.AuthPage.info2": "Start a No-Risk Free Trial Now.",
  "app.containers.AuthPage.username": "Email",
  "app.containers.AuthPage.name": "Full Name",
  "app.containers.AuthPage.enterName": "Enter full name",
  "app.containers.AuthPage.email": "Email",
  "app.containers.AuthPage.maxLengthError": "Please enter less than {length} characters",
  "app.containers.AuthPage.passwordMinLength": "Please enter greater than {length} characters",
  "app.containers.AuthPage.phoneNumber": "Phone number",
  "app.containers.AuthPage.enterPhoneNumber": "Enter phone number",
  "app.containers.AuthPage.enterUsername": "Enter account",
  "app.containers.AuthPage.enterEmail": "Enter email",
  "app.containers.AuthPage.phoneNumberError": "Please enter Phone Number",
  "app.containers.AuthPage.password": "Password",
  "app.containers.AuthPage.enterPassword": "Enter Password",
  "app.containers.AuthPage.passwordError": "Please enter a Password",
  "app.containers.AuthPage.confirmPassword": "Confirm Password",
  "app.containers.AuthPage.confirmPasswordError": "Please enter Confirm Password",
  "app.containers.AuthPage.otpCode": "OTP Code",
  "app.containers.AuthPage.enterOtpCode": "Enter OTP",
  "app.containers.AuthPage.otpCodeError": "Please enter OTP",
  "app.containers.AuthPage.passwordPlaceHolder": "Password must be between 8 and 15 characters, with at least 1 alphanumeric",
  "app.containers.AuthPage.register_agree": "By register, you agree to our",
  "app.containers.AuthPage.and": "and",
  "app.containers.AuthPage.policy": "Privacy Policy",

  "app.containers.AuthPage.FormRegister.title":"Register",
  "app.containers.AuthPage.FormRegister.guidance":"To experience the features and utilities from the system.",
  "app.containers.AuthPage.FormRegister.FormRegister.submit":"Register",
  "app.containers.AuthPage.FormRegister.FormRegister.already_registered":"Do you already have an account?",
  "app.containers.AuthPage.FormRegister.FormRegister.link_signin":"Login",
  "app.containers.AuthPage.FormRegister.passwordPlaceHolder":"Password must be between 8 and 15 characters, with at least 1 alphanumeric",
  "app.containers.AuthPage.FormRegister.usernameRequiredError":"Please enter full name",
  "app.containers.AuthPage.FormRegister.emailRequiredError":"Please enter email",
  "app.containers.AuthPage.FormRegister.phoneRequiredError":"Please enter phone number",
  "app.containers.AuthPage.FormRegister.passwordRequiredError":"Password must be between 8 and 15 characters, with at least 1 alphanumeric",
  "app.containers.AuthPage.FormRegister.passwordMaxLengthError":"Please enter password less than 15 characters",
  "app.containers.AuthPage.FormRegister.passwordMinLengthError":"Password must be between 8 and 15 characters, with at least 1 alphanumeric",
  "app.containers.AuthPage.FormRegister.confirm_passwordRequiredError":"Please re-enter password",
  "app.containers.AuthPage.FormRegister.confirm_passwordMaxLengthError":"Please enter authentication password less than 30 characters",
  "app.containers.AuthPage.FormRegister.passwordNotMatchError":"Authentication password does not match",
  "app.containers.AuthPage.FormRegister.passwordStrongError":"Password must be between 8 and 15 characters, with at least 1 alphanumeric",
  "app.containers.AuthPage.FormRegister.noMaxLengthError":"Up to 30 characters",
  "app.containers.AuthPage.FormRegister.registerSuccess":"You have successfully registered. Please confirm email before use!",
  "app.containers.AuthPage.FormRegister.new_password":"Enter your new password",
  "app.containers.AuthPage.FormRegister.current_password":"Enter old password",
  "app.containers.AuthPage.FormRegister.confirm_new_password":"Confirm password",
  "app.containers.AuthPage.FormRegister.new_pass_title":"Create a new password",

  "app.containers.Routes.root": "Home",
  "app.containers.Routes.overview": "Overview",
  "app.containers.Routes.modem": "Modem",
  "app.containers.Routes.license": "License",
  "app.containers.Routes.proxy": "Proxy",
  "app.containers.Routes.customer": "Customer",
  "app.containers.Routes.transaction": "Transaction",
  "app.containers.Routes.pkg": "Package",
  "app.containers.Routes.notification": "Notification",
  "app.containers.Routes.dashboard": "Buy New Proxy",
  "app.containers.Routes.recharge": "Recharge",
  "app.containers.Routes.myproxy": "My Proxy",
  "app.containers.Routes.profile": "Account",
  "app.containers.Routes.configuration": "Configuration",
  "app.containers.Routes.api": "API Document",
  "app.containers.Routes.affiliate":"Affiliate",
  "app.containers.Routes.promotion":"Promotion",

  "app.components.common.logout": "Logout",
  "app.components.common.back": "Back",
  "app.components.common.title": "Change Password",
  "app.components.common.subTitle": "To change your password, please enter your phone number below",
  "app.components.common.currentPassword": "Current Password",
  "app.components.common.newPassword": "Enter new password",
  "app.components.common.confirmPassword": "Confirm Password",
  "app.components.common.messageSuccess": "Your password has been changed successfully.",
  "app.components.common.messageInvalidNewPassword": "The new password must not be the same as the old password.",
  "app.components.common.currentPasswordRequiredError": "Please enter current password.",
  "app.components.common.new_passwordRequiredError": "Please enter a new password.",
  "app.components.common.new_passwordMaxLengthError": "Please enter a new password less than 30 characters.",
  "app.components.common.new_passwordMinLengthError": "Please enter more than 8 characters.",
  "app.components.common.new_passwordStrongError": "Password must be between 8 and 15 characters, with at least 1 alphanumeric.",
  "app.components.common.confirm_passwordRequiredError": "Please re-enter your password.",
  "app.components.common.passwordNotMatchError": "Authentication password does not match.",
  "app.components.common.noDataFound": "No data found",
  "app.components.common.optional": "optional",
  "app.components.common.submitError": "Please double check the information",
  "app.components.common.noOptional": "No option.",
  "app.components.common.tableLoading": "Loading...",
  "app.components.common.noOptionMessage": "Not found",
  "app.components.common.selectMessage": "Please select",
  "app.components.common.inputMessage": "Enter",
  "app.components.common.update": "Update",
  "app.components.common.readMore": "See more",
  "app.components.common.toggleOn": "On",
  "app.components.common.toggleOff": "Off",
  "app.components.common.requiredNotNull": "Please enter information",
  "app.components.common.close": "Close",
  "app.components.common.search": "Search",
  "app.components.common.allLabel": "All",
  "app.components.common.searchPlaceHolder": "Code, Name",
  "app.components.common.no": "No.",
  "app.components.common.status": "Status",
  "app.components.common.action": "Action",
  "app.components.common.create": "Create",
  "app.components.common.add": "Create",
  "app.components.common.edit": "Update",
  "app.components.common.msgUpdateSuccess": "Update successful",
  "app.components.common.msgUpdateFailed": "Update failed.",
  "app.components.common.msgCreateSuccess": "New successfully added",
  "app.components.common.msgCreateFailed": "Add new failed",
  "app.components.common.msgDeleteSuccess": "Data deletion successful",
  "app.components.common.msgDeleteFailed": "This data cannot be deleted",
  "app.components.common.notFound": "No data found",
  "app.components.common.clearAllFiltersButton": "Clear filters",
  "app.components.common.advancedSearchButton": "Advanced Search",
  "app.components.common.titleConfirm": "Confirmation message",
  "app.components.common.messageConfirm": "Do you want to delete this data?",
  "app.components.common.confirmButton": "Confirm",
  "app.components.common.cancelButton": "Cancel",
  "app.components.common.pkgInfo1": "Unlimited bandwidth",
  "app.components.common.pkgInfo2": "Support HTTP/SOCKS5",
  "app.components.common.pkgInfo3": "Authentication User:Pass or IP whitelist",
  "app.components.common.pkgInfo4": "Custom Rotation Time or use API to change IP",
  "app.components.common.pkgInfo5": "Minimum time to change IP from",
  "app.components.common.pkgInfo6": "dedicated SIM card",
  "app.components.common.day": "day",
  "app.components.common.week": "week",
  "app.components.common.month": "month",
  "app.components.common.buynow": "Buy now",
  "app.components.common.minute": "minute",
  "app.components.common.hour": "hour",
  "app.components.common.second": "second",
  "app.components.common.changeBtn": "Change",
  "app.components.common.editLabel": "Update Info",
  "app.components.common.addLabel": "Create New",
  "app.components.common.buyProxySuccess": "New purchase successful. Please check at My Proxy!",
  "app.components.common.buyProxyError": "New purchase failed. Please try again.",
  "app.components.common.purchaseTitle": "New purchase",
  "app.components.common.updatedSuccess": "Update successful",
  "app.components.common.updatedError": "Update failed",
  "app.components.common.createdSuccess": "New successfully added",
  "app.components.common.createdError": "Add new failed",
  "app.components.common.location": "Location",
  "app.components.common.balance": "Balance",
  "app.components.common.total_license": "Total license",
  "app.components.common.total_license_expired": "Total license expires today",
  "app.components.common.proxy_location": "Proxy Location",
  "app.components.common.proxy_package": "Proxy Package",
  "app.components.common.isp_network": "ISP Network",
  "app.components.common.available": "Available",
  "app.components.common.not_available": "Not Available",
  "app.components.common.expired_date": "Expired date",
  "app.components.common.change_ip": "Change IP",
  "app.components.common.authentication": "Authentication",
  "app.components.common.extend": "Extend",
  "app.components.common.choose_location": "Change Location",
  "app.components.common.export_file": "Export file",
  "app.components.common.confirm_change_ip": "Are you sure want to change IP?",
  "app.components.common.confirm_extend": "Are you sure want to extend this license?",
  "app.components.common.pkg_license": "Package / License",
  "app.components.common.start_expired_date": "Start/Expire Date",
  "app.components.common.proxy_detail": "Proxy Detail",
  "app.components.common.auto_rotation": "Auto rotation",
  "app.components.common.copy_success": "Copy to clipboard success!",
  "app.components.common.number_of_proxy": "Number of proxies",
  "app.components.common.proxy_auth_by": "Proxy authentication by",
  "app.components.common.tips_password_random": "Password will be randomly generated for each port. You can change it during use.",
  "app.components.common.password_random": "Password will be generated randomly for each port.",
  "app.components.common.ip_whitelist": "IP whitelist: IP1,IP2,.... You can change during use.",
  "app.components.common.total_amount": "Total amount",
  "app.components.common.buy": "Buy",
  "app.components.common.thank_for_recharge": "Thanks for your recharge. Please check at Transaction History! ",
  "app.components.common.system_interrupted": "System is interrupted. Please try again or contact support!",
  "app.components.common.error_top_up": "Error forward top-up transaction. Please try again ",
  "app.components.common.recharge_success": "Recharge successful. New balance ",
  "app.components.common.top_up_with": "TOPUP YOUR ACCOUNT WITH ",
  "app.components.common.crypto_now": "CRYPTO NOW ",
  "app.components.common.purchase_a_proxy": "TO PURCHASE A PROXY",
  "app.components.common.crypto_1": "1. Click the 'Add Funds' button to begin ",
  "app.components.common.crypto_2": "2. Choose the currency between BTC or USDT (Trc20) ",
  "app.components.common.crypto_3": "3. Input the amount you want to deposit, and create the order. ",
  "app.components.common.crypto_4": "4.Once payment has been completed, the funds will be available to purchase.",
  "app.components.common.card_now": "YOUR CARD",
  "app.components.common.card_1": "1. Click the 'Add Funds' button to begin.",
  "app.components.common.card_2": "2. Input the amount you want to deposit, and create the checkout.",
  "app.components.common.card_3": "3. You’re redirected to the Stripe Checkout payment form.",
  "app.components.common.card_4": "4. Fill out the payment details with the card information. ",
  "app.components.common.card_5": "5. Click Pay.",
  "app.components.common.card_6": "6. You’re redirected to your new success page. The funds will be available to purchase.",
  "app.components.common.topup_add": "Please top-up add",
  "app.components.common.deposit_money": "Deposit Money",
  "app.components.common.input_amount": "Input Amount",
  "app.components.common.price": "Price",
  "app.components.common.amount": "Amount",
  "app.components.common.address": "Address",
  "app.components.common.tip_coin_1": "Please sends coins with the transfer information.",
  "app.components.common.tip_coin_2": "After sends coins, system will processes and exchanges them, and settles the payment to your balance.",
  "app.components.common.min_amount": "Min Amount",
  "app.components.common.est_amount": "Estimate Amount",
  "app.components.common.created_date": "Created date",
  "app.components.common.trans_type": "Transaction type",
  "app.components.common.description": "Description",
  "app.components.common.trans_info": "Transfer Information",
  "app.components.common.guide": "Guide",
  "app.components.common.trans_his": "Transaction History",
  "app.components.common.affiliate_title": "The affiliate program is running and super hot ",
  "app.components.common.aff_info_1": "You will receive",
  "app.components.common.aff_info_1_2": "commission from any deposit of the friends you have referred. ",
  "app.components.common.aff_info_2": "commission will be effective immediately upon successful deposit transaction from your friends. ",
  "app.components.common.aff_info_3": "The system will keep cookies for 60 days from the time you click on your Affiliate link ",
  "app.components.common.aff_info_4": "Please share your referral link with your friends who register for an account in the system ",
  "app.components.common.aff_info_5": "Your referral link",
  "app.components.common.username": "Username",
  "app.components.common.name": "Name",
  "app.components.common.email": "Email",
  "app.components.common.phone_number": "Phone number",
  "app.components.common.received_email": "Received email ",
  "app.components.common.change": "Change",
  "app.components.common.profile": "Profile"
}
