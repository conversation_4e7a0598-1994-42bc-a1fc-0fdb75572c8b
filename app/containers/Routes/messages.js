import { defineMessages } from 'react-intl';
// import { get, toLower } from 'lodash';
import get from 'lodash/get';
import toLower from 'lodash/toLower';

const scope = 'app.containers.Routes';

export const messages = defineMessages({
  '/': {
    id: `${scope}.root`,
    defaultMessage: 'Home',
  },
  overview: {
    id: `${scope}.overview`,
    defaultMessage: 'Overview',
  },
  modem: {
    id: `${scope}.modem`,
    defaultMessage: 'Modem',
  },
  license: {
    id: `${scope}.license`,
    defaultMessage: 'License',
  },
  proxy: {
    id: `${scope}.proxy`,
    defaultMessage: 'Proxy',
  },
  customer: {
    id: `${scope}.customer`,
    defaultMessage: 'Customer',
  },
  transaction: {
    id: `${scope}.transaction`,
    defaultMessage: 'Transaction',
  },
  pkg: {
    id: `${scope}.pkg`,
    defaultMessage: 'Package',
  },
  notification: {
    id: `${scope}.notification`,
    defaultMessage: 'Notification',
  },
  dashboard: {
    id: `${scope}.dashboard`,
    defaultMessage: 'Buy new Proxy',
  },
  recharge: {
    id: `${scope}.recharge`,
    defaultMessage: 'Top-up',
  },
  myproxy: {
    id: `${scope}.myproxy`,
    defaultMessage: 'My Proxy',
  },
  profile: {
    id: `${scope}.profile`,
    defaultMessage: 'Profile',
  },
  configuration: {
    id: `${scope}.configuration`,
    defaultMessage: 'Configuration',
  },
  monitor: {
    id: `${scope}.monitor`,
    defaultMessage: 'Notification',
  },
  affiliate: {
    id: `${scope}.affiliate`,
    defaultMessage: 'Affiliate',
  },
  promotion: {
    id: `${scope}.promotion`,
    defaultMessage: 'Promotion',
  },
  mail: {
    id: `${scope}.mail`,
    defaultMessage: 'Mail',
  },
  help: {
    id: `${scope}.help`,
    defaultMessage: 'Help',
  },
});

export function getMessageByPathSegment(segment) {
  const lowercaseSegment = toLower(segment);
  return get(messages, lowercaseSegment, null);
}
