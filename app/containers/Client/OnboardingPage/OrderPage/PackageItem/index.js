import React from 'react';
import {compose} from 'redux';
import {injectIntl} from 'react-intl';
import styled from 'styled-components';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import {Card, Elevation} from '@blueprintjs/core';
import messages from '../../messages';
import get from 'lodash/get';

const StyledComponent = styled.div`

`;


export class PackageItem extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      userInfo: {},
    };
  }

  render() {
    let {
      salePackages,
      newOrderInfo,
      onClick,
      intl,
    } = this.props;

    console.log('--package item: ' + JSON.stringify(newOrderInfo));

    const salePkg = salePackages.find(el => {
      return el.location === get(newOrderInfo, 'location', el.location)
        && el.isp === get(newOrderInfo, 'isp', el.isp)
        && el.packageUnit === get(newOrderInfo, 'unit', el.packageUnit)
        && el.duration === get(newOrderInfo, 'duration', el.duration);
    });

    const unit = get(salePkg, 'packageUnit', '');

    return (
      <StyledComponent>
        <Card
          interactive
          elevation={Elevation.TWO}
          className="callout callout-warning"
        >
          <div className="d-flex flex-column pl-3">
            {salePkg && <h8
              className="text-black-5"> ✔ {salePkg.packageUnit === 'DAY' ? `${salePkg.duration * 24} ${intl.formatMessage(messages.hour)}` : `${salePkg.duration} ${unit}`} {intl.formatMessage(messages.pkgInfo6)}</h8>}
            <h8 className="text-black-5"> ✔ Unlimited bandwidth</h8>
            <h8 className="text-black-5"> ✔ Support HTTP/SOCKS5/UDP/VPN</h8>
            <h8 className="text-black-5"> ✔ Support IPv4/IPv6</h8>
            <h8 className="text-black-5"> ✔ Auth. User: Pass / IP Whitelist</h8>
            <h8 className="text-black-5"> ✔ Set Rotation Time / Use API to change IP</h8>
            {salePkg && <h8
              className="text-black-5"> ✔ {intl.formatMessage(messages.pkgInfo5)} {salePkg.minTimeChangeIp >= 60 ? ` ${salePkg.minTimeChangeIp / 60} ${intl.formatMessage(messages.minute)}` : ` ${salePkg.minTimeChangeIp} ${intl.formatMessage(messages.second)}`}
            </h8>}
          </div>
        </Card>
      </StyledComponent>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(PackageItem);
