import React from 'react';
import {compose} from 'redux';
import Row from 'reactstrap/es/Row';
import Col from 'reactstrap/es/Col';
import {injectIntl} from 'react-intl';
import Container from 'reactstrap/es/Container';
import Card from 'components/Card';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import DropdownList from 'components/DropdownList';
import {convertDropdownList} from 'utils/utilHelper';
import get from 'lodash/get';
import StyledComponent from './styles';
import Button from 'components/common/Button';
import ATaTIcon from 'images/isp/atat.png';
import VerizonIcon from 'images/isp/verizon.png';
import TMobileIcon from 'images/isp/tmobile.png';
import OpenVpnIcon from 'images/addon/open_vpn.png';
import WireGuardIcon from 'images/addon/wire_guard.png';
import AddonItem from './AddonItem';
import ISPItem from './ISPItem';
import PackageItem from './PackageItem';
import isEmpty from "lodash/isEmpty";

class OrderPage extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      listIspSelected: [],
      locations: [],
      units: []
    };
  }

  componentWillReceiveProps(nextProps) {

  }

  componentDidMount() {
    const {intl, salePackages, newOrderInfo} = this.props;
    console.log(newOrderInfo);

    const {locationId, location} = newOrderInfo;
    this.handleUpdateLocation(locationId, location);
  }

  handleUpdateLocation = (id, location) => {
    const {intl, salePackages, handleUpdateOrder} = this.props;

    handleUpdateOrder('locationId', id);
    handleUpdateOrder('location', location);

    const listPackageSelected = [...new Set(salePackages.filter(item => item.location === location))];
    const listIspSelected = [...new Set(listPackageSelected.map(item => item.isp))];

    this.setState({
      listIspSelected: listIspSelected
    })
  }

  validationOrder = () => {
    const {handleCheckoutStep, handleUpdateSalePackage, newOrderInfo, salePackages} = this.props;

    console.log('--------- VALIDATE ORDER INFO --------');
    console.log(newOrderInfo);

    let isValidateOrder = true;
    if (isEmpty(get(newOrderInfo, 'location'))) {
      isValidateOrder = false;
      this.props.handleAlertError('Please select a location!');
    } else if (isEmpty(get(newOrderInfo, 'isp'))) {
      isValidateOrder = false;
      this.props.handleAlertError('Please select a ISP Network!');
    } else if (isEmpty(get(newOrderInfo, 'unit'))) {
      isValidateOrder = false;
      this.props.handleAlertError('Please select a plan!');
    } else if (get(newOrderInfo, 'quantity') === undefined) {
      isValidateOrder = false;
      this.props.handleAlertError('Please input a quantity!!');
    }

    if (isValidateOrder) {
      const salePackage = salePackages.find(el => {
        return el.location === newOrderInfo.location
          && el.isp === newOrderInfo.isp
          && el.packageUnit === newOrderInfo.unit
          && el.duration === newOrderInfo.duration;
      });

      if (!salePackage) {
        this.props.handleAlertError('No sale package found. Please try again.!');
      } else {
        handleUpdateSalePackage(salePackage);
        handleCheckoutStep();
      }
    }
  }


  render() {
    const {intl, salePackages, newOrderInfo, handleUpdateOrder, saleLocations, saleUnits, saleQuantities} = this.props;
    const {listIspSelected} = this.state;


    // ISP
    const ISP_T_MOBILE = "T-Mobile";
    const ISP_VERIZON = "Verizon Wireless";
    const ISP_ATaT = 'AT&T';

    // ADDON
    const ADDON_OPEN_VPN = "OPEN_VPN";
    const ADDON_WIRE_GUARD = "WIRE_GUARD";

    return (
      <StyledComponent>

        <Container>
          <div className="mt-1 container-fluid">
            <Card interactive className="card-info p-3">

              <div className="card-header">
                <h4 className="card-title">
                  <i className="fas fa-x"></i>
                  Buy new Proxy
                </h4>
              </div>

              <Row className="item-callout">
                <Col md={{size: 12}}>
                  <h5>#Location</h5>
                  <div>
                    <DropdownList
                      value={convertDropdownList(saleLocations).find((option) =>
                        option.value === get(newOrderInfo, 'locationId')
                      )}
                      options={convertDropdownList(saleLocations)}
                      name={'locationId'}
                      onChange={(option) => {
                        this.handleUpdateLocation(option.value, option.label);
                      }}
                    />
                  </div>

                </Col>
              </Row>

              <Row className="item-callout">
                <Col md={{size: 12}}>
                  <h5>#ISP Network</h5>
                  <Row>
                    <Col md={{size: 4}} className="mb-3 p-2">
                      <ISPItem
                        icon={TMobileIcon}
                        isp={ISP_T_MOBILE}
                        location={get(newOrderInfo, 'location')}
                        value={get(newOrderInfo, 'isp')}
                        salePackages={salePackages}
                        status={listIspSelected.includes(ISP_T_MOBILE) ? 'available' : 'not available'}
                        onClick={() => {
                          handleUpdateOrder('isp', ISP_T_MOBILE);
                        }}
                      />
                    </Col>
                    <Col md={{size: 4}} className="mb-3 p-2">
                      <ISPItem
                        icon={VerizonIcon}
                        isp={ISP_VERIZON}
                        location={get(newOrderInfo, 'location')}
                        value={get(newOrderInfo, 'isp')}
                        salePackages={salePackages}
                        status={listIspSelected.includes(ISP_VERIZON) ? 'available' : 'not available'}
                        onClick={() => handleUpdateOrder('isp', ISP_VERIZON)}
                      />
                    </Col>
                    <Col md={{size: 4}} className="mb-3 p-2">
                      <ISPItem
                        icon={ATaTIcon}
                        isp={ISP_ATaT}
                        location={get(newOrderInfo, 'location')}
                        value={get(newOrderInfo, 'isp')}
                        salePackages={salePackages}
                        status={listIspSelected.includes(ISP_ATaT) ? 'available' : 'not available'}
                        onClick={() => {
                          handleUpdateOrder('isp', ISP_ATaT);
                        }}
                      />
                    </Col>
                  </Row>
                </Col>
              </Row>

              <Row className="item-callout">
                <Col md={{size: 12}}>
                  <h5>#Plan</h5>
                  <div>
                    <Row>
                      <Col md={{size: 7}}>
                        <DropdownList
                          label={'Time/Period'}
                          value={convertDropdownList(saleUnits).find((option) =>
                            option.value === get(newOrderInfo, 'unit')
                          )}
                          options={convertDropdownList(saleUnits)}
                          name={'location'}
                          onChange={(option) => {
                            console.log(option.value);

                            let pkgUnit = option.value.split('-')[0];
                            let pkgDuration = option.value.split('-')[1];

                            handleUpdateOrder('unit', pkgUnit, null, 'duration', parseInt(pkgDuration));
                          }}
                        />
                      </Col>
                      <Col md={{size: 5}}>
                        <PackageItem
                          salePackages={salePackages}
                          newOrderInfo={newOrderInfo}
                        />
                      </Col>
                    </Row>
                  </div>
                </Col>
              </Row>

              <Row className="item-callout">
                <Col md={{size: 12}}>
                  <h5>#Add on</h5>
                  <div>
                    <Row>
                      <Col md={{size: 3}} className="mb-3 p-2">
                        <AddonItem
                          icon={OpenVpnIcon}
                          addon={ADDON_OPEN_VPN}
                          value={get(newOrderInfo, 'vpn')}
                          onClick={() => {
                            if (get(newOrderInfo, 'vpn') === ADDON_OPEN_VPN)
                              handleUpdateOrder('vpn', null);
                            else
                              handleUpdateOrder('vpn', ADDON_OPEN_VPN);
                          }}
                        />
                      </Col>
                      <Col md={{size: 3}} className="mb-3 p-2">
                        <AddonItem
                          icon={WireGuardIcon}
                          addon={ADDON_WIRE_GUARD}
                          value={get(newOrderInfo, 'vpn')}
                          onClick={() => {
                            if (get(newOrderInfo, 'vpn') === ADDON_WIRE_GUARD)
                              handleUpdateOrder('vpn', null);
                            else
                              handleUpdateOrder('vpn', ADDON_WIRE_GUARD);
                          }}
                        />
                      </Col>
                    </Row>
                    {get(newOrderInfo, 'vpn') === ADDON_WIRE_GUARD && <Row>
                      <Col md={{size: 12}}>
                        <div className="callout callout-warning">
                          <h7 className="text-black-51"> ✔ WireGuard is a communication protocol and free and
                            open-source
                            software that implements encrypted virtual private networks.
                          </h7>
                          <br/>
                          <h7 className="text-black-51">✔ It aims to be lighter and better performing than IPsec and
                            OpenVPN,
                            two common tunneling protocols.
                          </h7>
                          <br/>
                          <h7 className="text-black-51">✔ The WireGuard protocol passes traffic over UDP
                          </h7>
                        </div>
                      </Col>
                    </Row>}
                    {get(newOrderInfo, 'vpn') === ADDON_OPEN_VPN && <Row>
                      <Col md={{size: 12}}>
                        <div className="callout callout-warning">
                          <h7 className="text-black-51"> ✔ OpenVPN is a virtual private network system that
                            implements
                            techniques to create secure point-to-point or site-to-site connections in routed
                            or bridged configurations and remote access facilities
                          </h7>
                          <br/>
                          <h7 className="text-black-51">✔ OpenVPN is a robust and highly flexible VPN daemon.
                            OpenVPN
                            supports SSL/TLS security, ethernet bridging,
                            TCP or UDP tunnel transport through proxies or NAT.
                          </h7>
                        </div>
                      </Col>
                    </Row>}
                  </div>
                </Col>
              </Row>

              <Row className="item-callout">
                <Col md={{size: 12}}>
                  <h5>#Number of proxies</h5>
                  <div>
                    <DropdownList
                      label={'Time/Period'}
                      value={convertDropdownList(saleQuantities).find((option) =>
                        option.value === get(newOrderInfo, 'quantity')
                      )}
                      options={convertDropdownList(saleQuantities)}
                      name={'quantity'}
                      onChange={(option) => {
                        const promotion = saleQuantities.find(el => {
                          return option.value === el.id;
                        });

                        if (promotion) {
                          handleUpdateOrder('quantity', option.value, null,
                            'quantityPromotionId', promotion.promotionId,
                            'quantityPromotionPercent', promotion.percent);
                        } else {
                          handleUpdateOrder('quantity', option.value);
                        }
                      }}
                    />
                    {/*<FormInputGroup*/}
                    {/*  didCheckErrors={false}*/}
                    {/*  name="number"*/}
                    {/*  onChange={(e) => {*/}
                    {/*    handleUpdateOrder('quantity', e.target.value);*/}
                    {/*  }}*/}
                    {/*  type={'number'}*/}
                    {/*  value={get(newOrderInfo, 'quantity')}*/}
                    {/*  placeholder={''}*/}
                    {/*/>*/}
                  </div>
                </Col>
              </Row>

              <div className="d-flex flex-column align-items-center">
                <Button
                  primary
                  type="button"
                  className="min-width-300 mt-2 mb-5"
                  loading={false}
                  onClick={() => {
                    this.validationOrder();
                  }}
                >{'Next'}</Button>
              </div>

            </Card>

          </div>
        </Container>
      </StyledComponent>
    );
  }
}


export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(OrderPage);
