import React, {Fragment} from 'react';
import StyledContainer from 'containers/Client/OnboardingPage/styles';
import {forwardTo} from '../../../utils/history';
import {routes} from 'containers/Routes/routeHelper';
import {injectIntl} from 'react-intl';
import {compose} from 'redux';
import messages from './messages';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import {Card} from '@blueprintjs/core';
import {Col, Row} from 'reactstrap';
import {getClientPackages} from 'services/admin/package.service';
import {getAllLocations, getPromotionQuantity, getUserBalance} from 'services/user.service';
import {formatCurrency} from 'utils/numberHelper';
import OrderPage from './OrderPage';
import CheckoutPage from './CheckoutPage';
import Container from 'reactstrap/es/Container';
import {getCurrencies} from 'services/payment.service';
import GuidePopup from "../RechargePage/GuidePopup";

export class OnboardingPage extends React.Component {

  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      salePackages: [],
      locations: [],
      salePackage: null,
      isOpenPopup: false,
      forceRefresh: false,
      config: {
        balance: 0,
        totalLicense: 0,
        totalExpiredLicense: 0,
      },
      isOrderStep: true,
      isCheckoutStep: false,
      newOrderInfo: {},
      currencies: [],
      saleLocations: [],
      saleUnits: [],
      saleQuantities: [],
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.handleGetUserInfo();
    this.handleGetPackages();
    this.handleGetLocations();
    this.handleGetPromotions();
    this.loadInitData();
  }

  handleGetPromotions = () => {
    const requestBody = {
      filtered: [{
        id: 'status',
        value: 'ACTIVE',
      }],
      pageSize: 20,
      page: 0,
    };
    this.props.handlePromise(getPromotionQuantity(requestBody), (response) => {
      const {data} = response;
      this.setState({
        saleQuantities: data,
      });
    });
  }

  loadInitData = async () => {
    const resp = await getCurrencies();
    this.setState({
      currencies: resp && resp.code === 1 ? resp.data : [],
    });
  }

  handleGetLocations = () => {
    this.props.handlePromise(getAllLocations(), (response) => {
      const {data} = response;
      this.setState({
        locations: data,
      });
    });
  }

  handleGetPackages = () => {
    const requestBody = {
      filtered: [{
        id: 'status',
        value: 'ACTIVE',
      }],
      pageSize: 20,
      page: 0,
    };

    this.props.handlePromise(getClientPackages(requestBody), (response) => {
      const {data} = response.data;

      // -------------- Location --------------
      let locations = [];
      const listLocationSelected = [...new Set(data.map(item => item.location))];
      for (let i = 0; i < listLocationSelected.length; i++) {
        locations.push({
          id: i + 100,
          name: listLocationSelected[i],
        });
      }

      // -------------- Unit --------------
      let unitMapping = {
        'MINUTES': 'Trial (15 minutes)',
        'DAY': 'Day',
        'WEEK': 'Week',
        'MONTH': 'Months',
      }
      let arrayUnits = ['MINUTES', 'DAY', 'WEEK', 'MONTH'];
      let units = [];
      for (let i = 0; i < arrayUnits.length; i++) {
        let pkgUnit = arrayUnits[i];
        for (let j = 1; j <= 15; j++) {
          let item = data.find(el => (el.packageUnit === pkgUnit && el.duration === j));
          if (item) {
            let unitId = item.packageUnit + '-' + item.duration;
            let unitName = item.duration + ' ' + unitMapping[item.packageUnit];
            if (item.promotionDes) {
              unitName += ' ' + item.promotionDes;
            }
            units.push({
              id: unitId,
              name: unitName,
            });
          }
        }
      }

      this.setState({
        salePackages: data,
        saleLocations: locations,
        saleUnits: units,
      });
    });
  }

  handleGetUserInfo = () => {
    this.props.handlePromise(getUserBalance(), (response) => {
      this.setState({
        config: {
          ...response,
        },
      });
    });
  }

  handleCheckoutStep = () => {
    this.setState({
      isCheckoutStep: true,
      isOrderStep: false
    })
  }

  handleOrderStep = () => {
    this.setState({
      isCheckoutStep: false,
      isOrderStep: true
    })
  }

  handleUpdateOrder = (key, value, callback = null, ...rest) => {
    console.log('Key: ' + key + '; Value: ' + value);

    let {newOrderInfo} = this.state;

    // Build update object từ key/value chính và rest
    const updateData = {[key]: value};

    for (let i = 0; i < rest.length; i += 2) {
      const k = rest[i];
      const v = rest[i + 1];
      if (k !== undefined) {
        updateData[k] = v;
      }
    }

    this.setState({
      newOrderInfo: {
        ...newOrderInfo,
        ...updateData,
      }
    });

    console.log('--------- ORDER INFO --------');
    console.log({
      ...newOrderInfo,
      ...updateData,
    });

    if (callback) callback();
  }


  handleUpdateSalePackage = (salePackage) => {
    this.setState({
      salePackageSelected: salePackage
    });
  }

  handleCryptoPayment = (isOpen = true, cryptoPaymentObject) => {
    this.handleOrderStep();
    this.setState({
      isOpenGuide: isOpen,
      cryptoPaymentObject,
    });
  }

  handleStripePayment = (paymentUrl) => {
    this.handleOrderStep();
    window.open(paymentUrl, '_blank');
  }

  render() {
    const {intl} = this.props;
    const {
      salePackages,
      salePackageSelected,
      config,
      newOrderInfo,
      isCheckoutStep,
      isOrderStep,
      currencies,
      isOpenGuide,
      cryptoPaymentObject,
      saleLocations,
      saleUnits,
      saleQuantities,
    } = this.state;

    return (
      <StyledContainer>
        <Fragment>
          <Container>
            <Row className="mt-3 mb-3">
              <Col md={{size: 3}}>
                <Card interactive className="card-info"
                      onClick={() => forwardTo(routes.CLIENT_RECHARGE)}>
                  <blockquote>
                    <h6>{intl.formatMessage(messages.balance)}: <b>{formatCurrency(config.balance)} USD</b>
                    </h6>
                  </blockquote>
                </Card>
              </Col>
              <Col md={{size: 3}}>
                <Card interactive className="card-info" onClick={() => forwardTo(routes.CLIENT_PROXY)}>
                  <blockquote>
                    <h6>{intl.formatMessage(messages.total_license)}: <b>{config.totalLicense}</b>
                    </h6>
                  </blockquote>
                </Card>
              </Col>
              <Col md={{size: 4}}>
                <Card interactive className="card-info" onClick={() => forwardTo(routes.CLIENT_PROXY)}>
                  <blockquote>
                    <h6>{intl.formatMessage(messages.total_license_expired)}: <b>{config.totalExpiredLicense}</b>
                    </h6>
                  </blockquote>
                </Card>
              </Col>

            </Row>
          </Container>

          {isOrderStep && <OrderPage
            newOrderInfo={newOrderInfo}
            salePackageSelected={salePackageSelected}
            handleOrderStep={() => this.handleOrderStep()}
            handleCheckoutStep={() => this.handleCheckoutStep()}
            handleUpdateOrder={this.handleUpdateOrder}
            handleUpdateSalePackage={this.handleUpdateSalePackage}
            salePackages={salePackages}
            saleLocations={saleLocations}
            saleUnits={saleUnits}
            saleQuantities={saleQuantities}
            className="pt-5"
          ></OrderPage>}
          {isCheckoutStep && <CheckoutPage
            newOrderInfo={newOrderInfo}
            handleCheckoutStep={() => this.handleCheckoutStep()}
            handleOrderStep={() => this.handleOrderStep()}
            handleUpdateOrder={this.handleUpdateOrder}
            salePackageSelected={salePackageSelected}
            className="pt-5"
            currencies={currencies}
            handleCryptoPayment={this.handleCryptoPayment}
            handleStripePayment={this.handleStripePayment}
            userBalance={config.balance}
          ></CheckoutPage>}

          <GuidePopup
            isOpen={isOpenGuide}
            handleOnClose={() => this.handleCryptoPayment(false)}
            transaction={cryptoPaymentObject}
          />

        </Fragment>
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(OnboardingPage);
