import React, {Fragment} from 'react';
import {injectIntl} from 'react-intl';
import messages from '../messages';
import {formatDataList, bytesToSize} from 'utils/utilHelper';
import TransactionList from 'components/Transactions/TransactionList';
import {getLicenses} from 'services/user.service';
import WithHandlePromise from 'containers/WithHandlePromise';
import {compose} from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import auth from 'utils/auth';
import {permission} from 'constants/permission';
import get from 'lodash/get';
import moment from 'moment';
import ButtonCopy from 'components/common/ButtonCopy';
import CheckboxPackage from 'components/Transactions/CheckboxPackage';
import ProxyInfo from 'components/common/ProxyInfo';
import Button from 'components/common/Button';
import ButtonActionGroup from 'components/common/ButtonActionGroup';
import styled, {css} from 'styled-components';
import env from 'env';

const getColumns = ({
                      intl,
                      selectedIds,
                      handleSelectRow,
                      handleAction,
                      togglePopup,
                      handleSelectOneRow,
                      handleSelectAllRow,
                      isSelectedAll,
                      togglePopupLocation,
                      handleImmediateChangeIp,
                      togglePopupAutoRotation,
                      handleAlertSuccess,
                      togglePopupExtend,
                      togglePopupManageLicense,
                      isConfirmLoading,
                      handleChangeVpn,
                      downloadVpn,
                      togglePopupTcpOs,
                    }) => (
  [
    {
      Header: intl.formatMessage(messages.no),
      accessor: 'index',
      headerClassName: 'table-header',
      width: 35,
    },
    {
      Header: <CheckboxPackage
        value={'ALL'}
        toggleSelection={(val, isSelected) => {
          handleSelectAllRow(!isSelected);
        }}
        isSelected={(val) => isSelectedAll}
      />,
      accessor: 'uuid',
      Cell: (row) => {
        const rowItem = row.value === undefined ? row.original.uuid : row.value;
        return (
          <CheckboxPackage
            value={rowItem}
            toggleSelection={(val, isSelected) => {
              handleSelectRow(val, !isSelected);
            }}
            isSelected={(val) => selectedIds.includes(val)}
            full={null}
          />
        );
      },
      maxWidth: 50,
      sortable: false,
      headerStyle: {justifyContent: 'center'},
      style: {justifyContent: 'center'},
    },
    {
      Header: intl.formatMessage(messages.pkg_license),
      accessor: 'id',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        const val = row.original;
        let licenseLocation = get(val, 'location');
        if (licenseLocation === '') {
          licenseLocation = 'Unlimited';
        }
        return (<div style={{display: 'flex', flexDirection: 'column', fontSize: 14}}>
            <span style={{textAlign: 'left'}}><i className="fa fa-box-open"
                                                 title="Package"/> {get(val, 'salePackage.name')}</span>
            <span style={{textAlign: 'left'}}><i className="fa fa-location-circle" title="Location"/> {licenseLocation}</span>
            <ButtonCopy content={val.uuid} isBtnCopy1 copyValue={val.uuid} btnText={'Copy license'}
                        style={{fontSize: '12px'}}/>
          </div>
        );
      },
      width: 330,
    },
    {
      Header: intl.formatMessage(messages.start_expired_date),
      accessor: 'startDate',
      headerClassName: 'table-header',
      sortable: false,
      width: 160,
      Cell: (row) => {
        const val = row.original;
        return (<div style={{display: 'flex', flexDirection: 'column', fontSize: 14}}>
            <span style={{textAlign: 'left'}}><i className="fa fa-calendar"
                                                 title="Start date"/> {moment(val.startDate).format('DD/MM/YY HH:mm:ss')}</span>
            <span style={{textAlign: 'left'}}><i className="fa fa-calendar-check"
                                                 title="Expiration date"/> {moment(val.expiredDate).format('DD/MM/YY HH:mm:ss')}</span>
          </div>
        );
      },
    },
    {
      Header: intl.formatMessage(messages.status),
      accessor: 'status',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        const statusTxt = row.value === 'ACTIVE' ? 'Active' : 'Expired';
        return (<Fragment><b>{statusTxt}</b></Fragment>);
      },
      width: 70,
    },
    {
      Header: intl.formatMessage(messages.proxy_detail),
      accessor: 'id',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        const val = row.original;
        if (val.status === 'EXPIRED') {
          return '';
        }
        return (<ProxyInfo
          httpProxy={val.httpProxy}
          sockProxy={val.sockProxy}
          location={get(val, 'httpProxy.modem.location')}
          proxy={get(val, 'salePackage.name')}
          onClickSync={() => {
            handleImmediateChangeIp(val.uuid);
          }}
          vpn={get(val, 'vpnType')}
          isConfirmLoading={isConfirmLoading}
          handleChangeVpn={() => {
            handleChangeVpn(val.uuid, get(val, 'vpnType'));
          }}
          downloadVpn={() => {
            downloadVpn(val.uuid)
          }}
        />);
      },
      width: 370,
    },
    {
      Header: 'Upload/Download Counter',
      accessor: 'status',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        const ul = get(row, 'original.httpProxy.counterUlUsedBytes', 0) + get(row, 'original.sockProxy.counterUlUsedBytes', 0);
        const dl = get(row, 'original.httpProxy.counterDlUsedBytes', 0) + get(row, 'original.sockProxy.counterDlUsedBytes', 0);
        return (<Fragment>{bytesToSize(ul)}/{bytesToSize(dl)}</Fragment>);
      },
      width: 170,
    },
    {
      Header: intl.formatMessage(messages.action),
      accessor: 'uuid',
      headerClassName: 'table-header',
      sortable: false,
      Cell: (row) => {
        if (row.original.status === 'EXPIRED') {
          const val = row.original.uuid;
          return (
            <Fragment>
              <div className="flex flex-column align-content-end action-btn">

                <div className="text-left">
                  <Button
                    primary
                    small
                    onClick={() => {
                      handleSelectOneRow(val);
                      handleAction('EXTEND_LICENSE');
                    }}
                    type="button"
                  ><i className="fa fa-redo"/> {intl.formatMessage(messages.extend)}</Button>
                </div>

              </div>
            </Fragment>
          );
        }

        const val = row.value;
        return (
          <Fragment>
            <div className="flex flex-column align-content-end action-btn">
              <div className="text-left">
                <Button
                  primary
                  small
                  onClick={() => {
                    handleSelectOneRow(val);
                    togglePopup(true);
                  }}
                  type="button"
                ><i className="fa fa-user-lock"/> {intl.formatMessage(messages.authentication)}</Button>
              </div>
              {/*<div className="text-left">*/}
              {/*  <Button*/}
              {/*    primary*/}
              {/*    small*/}
              {/*    onClick={() => {*/}
              {/*      handleSelectOneRow(val);*/}
              {/*      togglePopupExtend(true, get(row, 'original.salePackage.packageUnit'), get(row, 'original.location'));*/}
              {/*    }}*/}
              {/*    type="button"*/}
              {/*  ><i className="fa fa-redo"/> {intl.formatMessage(messages.extend)} </Button>*/}
              {/*</div>*/}
              <div className="text-left">
                <Button
                  primary
                  small
                  onClick={() => {
                    handleSelectOneRow(val);
                    togglePopupManageLicense(true, get(row, 'original.salePackage.packageUnit'), get(row, 'original.location'), get(row, 'original.salePackage.vpnFee', 5));
                  }}
                  type="button"
                ><i className="fa fa-cogs"/> Manage License</Button>
              </div>
              <div className="text-left">
                <Button
                  primary
                  small
                  onClick={() => {
                    handleSelectOneRow(val);
                    togglePopupLocation(true);
                  }}
                  type="button"
                ><i className="fa fa-map"/> {intl.formatMessage(messages.choose_location)}</Button>
              </div>
              <div className="text-left">
                <Button
                  primary
                  small
                  onClick={() => {
                    handleSelectOneRow(val);
                    togglePopupTcpOs(true);
                  }}
                  type="button"
                ><i className="fa fa-desktop"/> {intl.formatMessage(messages.tcp_ip_os)}</Button>
              </div>
              <div className="text-left">
                <Button
                  primary
                  small
                  onClick={() => {
                    handleSelectOneRow(val);
                    togglePopupAutoRotation(true, get(row, 'original.autoRotationTime'), get(row, 'original.salePackage.minTimeChangeIp'));
                  }}
                  type="button"
                ><i className="fa fa-sync"/> {intl.formatMessage(messages.auto_rotation)}</Button>
              </div>
              <div className="text-left">
                <ButtonActionGroup
                  primary
                  small
                  onClick={() => {
                  }}
                  type="button"
                  actions={[
                    {
                      text: 'API change IP',
                      onClick: () => {
                        const url = `${env.API_URL}/api/proxy/change-ip?license=${row.original.uuid}`;
                        navigator.clipboard.writeText(url);
                        handleAlertSuccess(intl.formatMessage(messages.copy_success));
                      },
                    },
                    {
                      text: 'API get status of proxy',
                      onClick: () => {
                        const url = `${env.API_URL}/api/proxy/status?license=${row.original.uuid}`;
                        navigator.clipboard.writeText(url);
                        handleAlertSuccess(intl.formatMessage(messages.copy_success));
                      },
                    },
                    {
                      text: 'API rotation setting',
                      onClick: () => {
                        const url = `${env.API_URL}/api/proxy/change-rotation-time?license=${row.original.uuid}&autoRotationTime=0`;
                        navigator.clipboard.writeText(url);
                        handleAlertSuccess(intl.formatMessage(messages.copy_success));
                      },
                    },
                  ]}
                ><i className="fa fa-globe-asia"/> Get Api <i className="fa fa-arrow-down"/></ButtonActionGroup>
              </div>
            </div>
          </Fragment>
        );
      },
    },
  ]
);

const DEFAULT_PAGES = -1;

const StyledComponent = styled.div`
  .action-btn {
    .bp3-button {
      font-size: 9px;
      padding: 0 3px 0 3px;
      margin-bottom: 1px;
      width: 100%;
    }
  }

`;

class ModemList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      isLoading: false,
      pages: DEFAULT_PAGES,
    };
  }

  fetchData = async (state) => {
    this.setState({isLoading: true});
    const {handleUpdateModemList} = this.props;
    const {page, pageSize, sorted} = state;
    const requestBody = {
      filtered: this.toFilteredList(),
      pageSize,
      page,
      sorted,
    };

    this.props.handlePromise(getLicenses(requestBody), (response) => {
      const {data, pages} = response.data;
      handleUpdateModemList(formatDataList(data, page, pageSize));
      this.setState({
        pages,
        isLoading: false,
      });
    });
  };

  toFilteredList = () => {
    const {filteredList} = this.props;
    return Object
      .entries(filteredList)
      .map((entry) => ({
        id: entry[0],
        value: Array.isArray(entry[1]) ? entry[1].join(',') : entry[1],
      }));
  };

  render() {
    const {
      intl,
      dataList,
      selectedIds,
      getKeyFromFilteredList,
      handleSelectRow,
      handleAction,
      togglePopup,
      handleSelectOneRow,
      handleSelectAllRow,
      isSelectedAll,
      togglePopupLocation,
      handleImmediateChangeIp,
      togglePopupAutoRotation,
      handleAlertSuccess,
      togglePopupExtend,
      togglePopupManageLicense,
      isConfirmLoading,
      handleChangeVpn,
      downloadVpn,
      togglePopupTcpOs,
    } = this.props;

    const {pages, isLoading} = this.state;
    const columns = getColumns({
      intl,
      selectedIds,
      handleSelectRow,
      handleAction,
      togglePopup,
      handleSelectOneRow,
      handleSelectAllRow,
      isSelectedAll,
      togglePopupLocation,
      handleImmediateChangeIp,
      togglePopupAutoRotation,
      handleAlertSuccess,
      togglePopupExtend,
      togglePopupManageLicense,
      isConfirmLoading,
      handleChangeVpn,
      downloadVpn,
      togglePopupTcpOs,
    });

    return (
      <StyledComponent>
        <TransactionList
          key={getKeyFromFilteredList()}
          manual
          data={dataList}
          pages={pages}
          loading={isLoading}
          columns={columns}
          onFetchData={this.fetchData}
          defaultSorted={[
            {
              id: 'createdDate',
              desc: true,
            },
          ]}
        />
      </StyledComponent>
    );
  }
}


export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl,
)(ModemList);
