import React from 'react';
import { injectIntl } from 'react-intl';
import Button from 'components/common/Button';
import ButtonLink from 'components/ButtonLink';
import ActionDialog from 'components/common/ActionDialog';
import { TO, convertDropdownList } from 'utils/utilHelper';
import messages from '../messages';
import styled from 'styled-components';
import { Formik, Form } from 'formik';
import { Row, Col } from 'reactstrap';
import get from 'lodash/get';
import DropdownList from 'components/common/DropdownList';
import { compose } from 'redux';
import WithHandleAlert from 'containers/WithHandleAlert';
import { updateTcpOs } from 'services/user.service';
import { errorCode } from 'constants/responseCode';
import * as Yup from 'yup';

const StyledContainer = styled(ActionDialog)`

`;

const TCP_OS_OPTIONS = [
  { value: 'linux', label: 'Linux' },
  { value: 'win1', label: 'Real Microsoft Windows 11 (v22H2)' },
  { value: 'win2', label: 'Real Microsoft Windows 10 (v21H2)' },
  { value: 'win3', label: 'Real Microsoft Windows 7 (v6.1)' },
  { value: 'mac1', label: 'Real Macbook Pro (macOS v15/v14/v13)' },
  { value: 'ipad1', label: 'Real Ipad Air Gen 4 (iOS v18)' },
  { value: 'ios1', label: 'Real iPhone 15 (iOS v17.7.1)' },
  { value: 'ios2', label: 'Real iPhone 13 (iOS v15.1.1)' },
  { value: 'android1', label: 'Real Galaxy S24 (android v14.0)' },
  { value: 'android2', label: 'Real Android (common device)' },
];

class TcpOsPopup extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      initData: {
        tcpOS: 'linux',
      },
    };
  }

  componentWillReceiveProps(nextProps) {
    if (nextProps.selectedIds !== undefined) {
      this.setState({
      });
    }
  }

  handleSubmit = async (values, { setSubmitting }) => {
    const { forceRefresh, handleOnClose, intl, selectedIds } = this.props;

    const dataSubmit = {
      licenseUuids: selectedIds,
      tcpOS: values.tcpOS,
    };

    const [err, response] = await TO(updateTcpOs(dataSubmit));
    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
      handleOnClose();
      forceRefresh();
    } else if (response.message) {
      this.props.handleAlertError(response.message);
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    setSubmitting(false);
  }

  render() {
    const self = this;
    const {
      intl,
      isOpen,
      handleOnClose,
    } = this.props;

    const {
      initData,
    } = self.state;

    const tcpOsOptions = convertDropdownList(TCP_OS_OPTIONS, '', '');

    return (
      <StyledContainer
        portalClassName="custom-portal"
        title={intl.formatMessage(messages.setup_tcp_os_profile)}
        usePortal
        canOutsideClickClose
        canEscapeKeyClose
        isOpen={isOpen}
        onClose={handleOnClose}
        width={550}
      >
        <Wrapper className="m-4">
          <div className="instruction-text mb-3">
            {intl.formatMessage(messages.tcp_os_instruction)}
          </div>
          <Formik
            onSubmit={this.handleSubmit}
            initialValues={initData}
            enableReinitialize
            validationSchema={Yup.object().shape({
              tcpOS: Yup.string().required(intl.formatMessage(messages.requiredNotNull)),
            })}
            render={(props) => (
              <Form>
                <Row>
                  <Col md={{ size: 12 }}>
                    <DropdownList
                      label={intl.formatMessage(messages.tcp_os_profile)}
                      value={tcpOsOptions.find((option) =>
                        option.value === get(props.values, 'tcpOS')
                      )}
                      isAsterisk
                      name="tcpOS"
                      options={tcpOsOptions}
                      onChange={(option) => {
                        props.setFieldValue('tcpOS', option.value);
                      }}
                    />
                  </Col>
                </Row>
                <div className="d-flex flex-column align-items-center">
                  <div className="d-flex">
                    <Button
                      primary
                      type="submit"
                      className="min-width-100 mt-4 mr-1"
                      loading={props.isSubmitting}
                    >{intl.formatMessage(messages.update)}</Button>
                  </div>
                  <ButtonLink
                    onClick={handleOnClose}
                    type={'button'}
                  >{intl.formatMessage(messages.close)}</ButtonLink>
                </div>
              </Form>
            )}
          />
        </Wrapper>
      </StyledContainer>
    );
  }
}

TcpOsPopup.propTypes = {};

const Wrapper = styled.div`
  margin-bottom: 10px;

  .instruction-text {
    line-height: 1.4;
    padding: 10px;
    background-color: ${(props) => props.theme.colors.gray100};
    border-radius: 4px;
    border-left: 4px solid ${(props) => props.theme.colors.warning};
  }

  .content {
    border: 1px solid ${(props) => props.theme.colors.gray300};
    display: flex;
    flex-wrap: wrap;

    .label {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.small12};
      opacity: 0.5;
    }

    .bold-text {
      color: ${(props) => props.theme.colors.black};
      font-size: ${(props) => props.theme.fontSizes.normal};
      font-weight: ${(props) => props.theme.fontWeights.strong};
      opacity: 0.8;
    }

    .group {
      padding: 10px 18px;
      background-color: ${(props) => props.theme.colors.white};
      width: 50%;

      &.gray {
        background-color: ${(props) => props.theme.colors.gray};
      }
    }
  }
`;

export default compose(
  WithHandleAlert,
  injectIntl
)(TcpOsPopup);
