import React, {Fragment} from 'react';
import StyledContainer from 'containers/Client/MyProxyPage/styles';
import Card from 'components/Card';
import ProxyList from './ProxyList';
import {routes} from 'containers/Routes/routeHelper';
import {injectIntl} from 'react-intl';
import {compose} from 'redux';
import messages from './messages';
import FilterDropdownGroupWrapper from 'components/FilterDropdownWrapper';
import {TO, convertDropdownList, formatDataList} from 'utils/utilHelper';
import DropdownList from 'components/DropdownList';
import ConfirmDialog from 'components/common/ConfirmDialog';
import WithHandlePromise from 'containers/WithHandlePromise';
import WithHandleAlert from 'containers/WithHandleAlert';
import FormInputDatePicker from 'components/common/FormInputDatePicker';
import {getLicenseStatusOptions} from './utils';
import Button from 'components/common/Button';
import AuthenticatePopup from './AuthenticatePopup';
import LocationPopup from './LocationPopup';
import TcpOsPopup from './TcpOsPopup';
import {
  changeIp,
  extendLicense,
  rebootDevice,
  changeVpn,
  downloadVpn,
  getUserBalance,
  getLicenses
} from 'services/user.service';
import {getLocations} from 'services/admin/location.service';
import {errorCode} from 'constants/responseCode';
import {getPackages} from 'services/admin/package.service';
import get from 'lodash/get';
import env from 'env';
import auth from 'utils/auth';
import isEmpty from 'lodash/isEmpty';
import AutoRotationPopup from './AutoRotationPopup';
import ExtendPopup from './ExtendPopup';
import ManageLicensePopup from './ManageLicensePopup';

export class ProxyWanList extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      dataList: [],
      isOpenAdvancedSearch: false,
      forceRefresh: false,
      filteredList: {
        expiredDate: null,
        status: 'ACTIVE',
      },
      isOpenPopup: false,
      isOpenPopupLocation: false,
      isOpenPopupTcpOs: false,
      isConfirm: false,
      isConfirmLoading: false,
      confirmType: '',
      isSelectedOption: false,
      selectedIds: [],
      selectedId: null,
      isSelectedAll: false,
      locations: [],
      packages: [],
      isOpenPopupRotation: false,
      isOpenPopupExtend: false,
      isOpenPopupManageLicense: false,
      vpnFee: 5, // Default value
    };
  }

  componentWillReceiveProps(nextProps) {
  }

  componentWillMount() {
    this.loadInitData();
  }

  loadInitData = async () => {
    const locations = await getLocations();
    this.setState({
      locations: locations.data.data,
    });

    const packages = await getPackages({
      filtered: [],
      pageSize: 100,
      page: 0,
    });
    this.setState({
      packages: packages.data.data,
    });
  }

  getKeyFromFilteredList = () => {
    const {filteredList, forceRefresh} = this.state;
    return `${filteredList.expiredDate}
            -${filteredList.status}
            -${forceRefresh}`;
  }

  handleUpdateModemList = (data) => {
    this.setState({
      dataList: data,
    });
  }

  handleSelectAllRow = (isSelected) => {
    const {dataList} = this.state;
    if (isSelected) {
      this.setState({
        selectedIds: dataList.map((i) => get(i, 'uuid')),
        isSelectedOption: true,
        isSelectedAll: true,
      });
    } else {
      this.setState({
        selectedIds: [],
        isSelectedOption: false,
        isSelectedAll: false,
      });
    }
  }

  handleSelectRow = (id, isSelected) => {
    let {selectedIds, dataList} = this.state;
    if (isSelected) {
      selectedIds.push(id);
    } else {
      selectedIds = selectedIds.filter((item) => item !== id);
    }
    this.setState({
      selectedIds,
      isSelectedAll: selectedIds.length === dataList.length,
      isSelectedOption: selectedIds.length > 0,
    });
  };


  forceRefresh = () => {
    this.setState({
      forceRefresh: !this.state.forceRefresh,
    });
  }

  handleSearch = (name, value) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        [name]: value,
      },
    }));
  }

  handleMultiSearch = (payload) => {
    this.setState((prevState) => ({
      filteredList: {
        ...prevState.filteredList,
        ...payload,
      },
    }));
  }

  handleSelectOneRow = (id) => {
    const {dataList} = this.state;
    const item = dataList.find((el) => el.uuid === id);
    const proxy = item.httpProxy;
    const modemType = get(proxy, 'modemType');

    this.setState({
      selectedId: id,
      selectedIds: [id],
      modemType,
    });
  }

  handleImmediateChangeIp = (id) => {
    console.log('id: ' + id);
    const {dataList} = this.state;
    const item = dataList.find((el) => el.uuid === id);
    const proxy = item.httpProxy;
    const modemType = get(proxy, 'modemType');

    this.setState({
      selectedId: id,
      selectedIds: [id],
      modemType,
      confirmType: 'CHANGE_IP',
    }, () => {
      this.handleAcceptConfirmPopup();
    });
  }

  handleAction = (type) => {
    this.setState({
      isConfirm: true,
      confirmType: type,
    });
  }

  handleExportExcel = () => {
    const userInfo = auth.getUserInfo();
    window.open(`${env.API_URL}/client/licenses/excel?customer=${userInfo.uuid}`, '_blank');
  }

  handleCopyFormat1 = () => {
    const {selectedIds, dataList} = this.state;
    const arrays = [];

    selectedIds.forEach((selectedId) => {
      const item = dataList.find((el) => el.uuid === selectedId);
      const proxy = item.httpProxy;
      const authen = get(proxy, 'authenticationUsers', '');
      const publicIp = get(proxy, 'host');
      const portV4Type2 = `${publicIp}:${get(proxy, 'sharedPort')}${isEmpty(authen) ? '' : `:${authen}`}`;
      arrays.push(portV4Type2);
    });

    navigator.clipboard.writeText(arrays.join('\r\n'));
    this.props.handleAlertSuccess('Copy to clipboard success!');
  }

  handleCopyFormat2 = () => {
    const {selectedIds, dataList} = this.state;
    const arrays = [];

    selectedIds.forEach((selectedId) => {
      const item = dataList.find((el) => el.uuid === selectedId);
      const proxy = item.httpProxy;
      const authen = get(proxy, 'authenticationUsers', '');
      const publicIp = get(proxy, 'host');
      const portV4 = `${isEmpty(authen) ? '' : `${authen}@`}${publicIp}:${get(proxy, 'sharedPort')}`;
      arrays.push(portV4);
    });

    navigator.clipboard.writeText(arrays.join('\r\n'));
    this.props.handleAlertSuccess('Copy to clipboard success!');
  }

  handleCopyLicense = () => {
    const {selectedIds, dataList} = this.state;
    const arrays = [];

    selectedIds.forEach((selectedId) => {
      const item = dataList.find((el) => el.uuid === selectedId);
      arrays.push(item.uuid);
    });

    navigator.clipboard.writeText(arrays.join('\r\n'));
    this.props.handleAlertSuccess('Copy to clipboard success!');
  }

  handleCloseConfirmPopup = () => {
    this.setState({
      isConfirm: false,
      selectedId: null,
      isConfirmLoading: false,
    });
  }

  togglePopup = (isOpen = true) => {
    this.setState({
      isOpenPopup: isOpen,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
        selectedIds: [],
      });
    }
  }

  togglePopupLocation = (isOpen = true) => {
    this.setState({
      isOpenPopupLocation: isOpen,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
        selectedIds: [],
        modemType: '',
      });
    }
  }

  togglePopupTcpOs = (isOpen = true) => {
    this.setState({
      isOpenPopupTcpOs: isOpen,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
        selectedIds: [],
      });
    }
  }

  handleAcceptConfirmPopup = async () => {
    const {confirmType, selectedIds} = this.state;
    if (confirmType === 'CHANGE_VPN') {
      this.handleChangeVpn();
    } else {
      this.setState({isConfirmLoading: true});
      const {intl} = this.props;

      const requestBody = {
        uuids: selectedIds,
      };

      let err,
        response;
      if (confirmType === 'CHANGE_IP') {
        [err, response] = await TO(changeIp(requestBody));
      } else if (confirmType === 'EXTEND_LICENSE') {
        [err, response] = await TO(extendLicense(requestBody));
      } else {
        [err, response] = await TO(rebootDevice(requestBody));
      }

      if (err) {
        this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
      }
      if (response.code === errorCode.SUCCESS) {
        this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
      } else {
        const msg = confirmType === 'EXTEND_LICENSE' ? ' Please check your account balance.' : ' Changing IP too fast.';
        this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed) + msg);
      }

      this.setState({
        isConfirm: false,
        isConfirmLoading: false,
      });
      this.forceRefresh();
    }
  }

  togglePopupAutoRotation = (isOpen = true, autoRotationTime, minPkgTime) => {
    this.setState({
      isOpenPopupRotation: isOpen,
      autoRotationTime,
      minPkgTime,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
        selectedIds: [],
      });
    }
  }

  togglePopupExtend = (isOpen = true, packageUnit, location) => {
    this.setState({
      isOpenPopupExtend: isOpen,
      packageUnit,
      locationExt: location,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
        selectedIds: [],
      });
    }
  }

  togglePopupManageLicense = (isOpen = true, packageUnit, location, vpnFee) => {
    console.log('togglePopupManageLicense: |' + packageUnit + '|' + location + '|' + vpnFee);

    this.setState({
      isOpenPopupManageLicense: isOpen,
      packageUnit,
      locationExt: location,
      vpnFee,
    });
    if (!isOpen) {
      this.setState({
        selectedId: null,
        selectedIds: [],
      });
    }
  }

  handleConfirmChangeVpn = async (uuid, currentVpn) => {
    this.setState({
      selectedId: uuid,
      currentVpn: currentVpn,
      isConfirm: true,
      confirmType: 'CHANGE_VPN',
    });
  }

  handleChangeVpn = async () => {
    let {selectedId} = this.state;

    this.setState({
      isVpnLoading: true,
      selectedId: null,
      isConfirmLoading: true,
    });

    const {intl} = this.props;
    const requestBody = {
      licenseUuid: selectedId,
    };

    let [err, response] = await TO(changeVpn(requestBody));

    if (err) {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }
    if (response.code === errorCode.SUCCESS) {
      this.props.handleAlertSuccess(intl.formatMessage(messages.msgUpdateSuccess));
    } else {
      this.props.handleAlertError(intl.formatMessage(messages.msgUpdateFailed));
    }

    this.setState({
      isConfirm: false,
      isConfirmLoading: false,
      isVpnLoading: false,
    });
    this.forceRefresh();
  }

  downloadVpn = async (uuid) => {
    window.open(`${env.API_URL}/client/vpn/download?licenseUuid=` + uuid, '_blank');
  }

  getConfirmMessage = () => {
    const {intl} = this.props;
    const {
      confirmType,
      currentVpn,
    } = this.state;

    if (confirmType === 'CHANGE_VPN') {
      if (currentVpn === 'OPEN_VPN') {
        return 'Your proxy is currently connected using OpenVPN. Switching to WireGuard will disconnect your current OpenVPN connection. Do you want to proceed with this change?';
      } else {
        return 'Your proxy is currently connected using WireGuard. Switching to OpenVPN will disconnect your current WireGuard connection. Do you want to proceed with this change?';
      }
    } else {
      return confirmType === 'CHANGE_IP' ? intl.formatMessage(messages.confirm_change_ip)
        : confirmType === 'EXTEND_LICENSE' ? intl.formatMessage(messages.confirm_extend)
          : 'Are you sure you want to perform reboot device?';
    }
  }

  componentDidMount() {
    const intervalId = setInterval(this.handleReloadProxy.bind(this), 5000);
    this.setState({intervalId});
  }

  componentWillUnmount() {
    clearInterval(this.state.intervalId);
  }

  handleReloadProxy(isFirst) {
    const {
      isOpenPopup,
      isOpenPopupLocation,
      isOpenPopupTcpOs,
      isOpenPopupRotation,
      isOpenPopupExtend,
      isOpenPopupManageLicense,
    } = this.state;
    if (isOpenPopup || isOpenPopupLocation || isOpenPopupTcpOs || isOpenPopupRotation || isOpenPopupExtend || isOpenPopupManageLicense) {
      return;
    }
    this.fetchProxyListData();
  }

  fetchProxyListData = async () => {
    const requestBody = {
      filtered: this.toFilteredList(),
      page: 0,
      pageSize: 20,
      sorted: {
        id: 'createdDate',
        desc: true,
      },
    };

    this.props.handlePromise(getLicenses(requestBody), (response) => {
      const {data, pages} = response.data;
      this.handleUpdateModemList(formatDataList(data, 0, 20));
    });
  };

  toFilteredList = () => {
    const {filteredList} = this.state;
    return Object
      .entries(filteredList)
      .map((entry) => ({
        id: entry[0],
        value: Array.isArray(entry[1]) ? entry[1].join(',') : entry[1],
      }));
  };

  render() {
    const {intl} = this.props;
    const {
      filteredList, dataList,
      isSelectedOption, selectedIds, selectedId,
      isConfirm, confirmType,
      isOpenPopup,
      isOpenPopupLocation,
      isOpenPopupTcpOs,
      isSelectedAll, isConfirmLoading,
      locations,
      modemType,
      isOpenPopupRotation,
      isOpenPopupExtend,
      isOpenPopupManageLicense,
      autoRotationTime,
      minPkgTime,
      packageUnit,
      packages,
      locationExt,
      currentVpn,
      vpnFee,
    } = this.state;
    const licenseOptions = convertDropdownList(getLicenseStatusOptions(intl), intl.formatMessage(messages.allLabel), '');
    let confirmMessage = this.getConfirmMessage();

    return (
      <StyledContainer>
        <Fragment>
          <Card>
            <div className="margin-bottom-13 d-flex justify-content-between">
              <FilterDropdownGroupWrapper>
                <div className="row no-gutters min-width-100">
                  <div className={'col-md-2'}>
                    <FormInputDatePicker
                      name="expiredDate"
                      value={filteredList.expiredDate}
                      label={intl.formatMessage(messages.expired_date)}
                      handleSearch={(name, value) => {
                        this.handleSearch(name, value);
                      }}
                    />
                  </div>
                  <div className="col-2">
                    <DropdownList
                      label={intl.formatMessage(messages.status)}
                      value={licenseOptions.find((option) =>
                        option.value === filteredList.status
                      )}
                      options={licenseOptions}
                      onChange={(option) => {
                        this.handleSearch('status', option.value);
                      }}
                    />
                  </div>
                </div>
              </FilterDropdownGroupWrapper>
            </div>
            <div className="margin-bottom-13 d-flex justify-content-between">

              <div className="mt-2">
                <Button
                  primary
                  disabled={!isSelectedOption}
                  small
                  onClick={() => this.handleAction('CHANGE_IP')}
                  type="button"
                  style={{fontSize: 10}}
                ><i className="fa fa-wrench"/> {intl.formatMessage(messages.change_ip)}</Button>
                <Button
                  primary
                  disabled={!isSelectedOption}
                  small
                  onClick={() => this.togglePopup(true)}
                  type="button"
                  style={{fontSize: 10, marginLeft: 5}}
                ><i className="fa fa-user-lock"/> {intl.formatMessage(messages.authentication)}</Button>
                {/*<Button*/}
                {/*primary*/}
                {/*disabled={!isSelectedOption}*/}
                {/*small*/}
                {/*onClick={() => this.handleAction('EXTEND_LICENSE')}*/}
                {/*type="button"*/}
                {/*style={{ fontSize: 10, marginLeft: 5 }}*/}
                {/*><i className="fa fa-redo" /> {intl.formatMessage(messages.extend)}</Button>*/}
                <Button
                  primary
                  small
                  disabled={!isSelectedOption}
                  onClick={() => this.handleCopyFormat1()}
                  type="button"
                  style={{fontSize: 10, marginLeft: 5}}
                ><i className="fa fa-copy"/> Copy format 1</Button>
                <Button
                  primary
                  small
                  disabled={!isSelectedOption}
                  onClick={() => this.handleCopyFormat2()}
                  type="button"
                  style={{fontSize: 10, marginLeft: 5}}
                ><i className="fa fa-copy"/> Copy format 2</Button>
                <Button
                  primary
                  small
                  disabled={!isSelectedOption}
                  onClick={() => this.handleCopyLicense()}
                  type="button"
                  style={{fontSize: 10, marginLeft: 5}}
                ><i className="fa fa-copy"/> Copy license</Button>
                <Button
                  primary
                  small
                  onClick={() => this.handleExportExcel()}
                  type="button"
                  style={{fontSize: 10, marginLeft: 5}}
                ><i className="fa fa-file-excel"/> {intl.formatMessage(messages.export_file)}</Button>
              </div>

            </div>
            <ProxyList
              dataList={dataList}
              selectedIds={selectedIds}
              filteredList={filteredList}
              handleUpdateModemList={this.handleUpdateModemList}
              getKeyFromFilteredList={this.getKeyFromFilteredList}
              handleSelectRow={this.handleSelectRow}
              handleSelectOneRow={this.handleSelectOneRow}
              handleSelectAllRow={this.handleSelectAllRow}
              isSelectedAll={isSelectedAll}
              forceRefresh={this.forceRefresh}
              handleAction={this.handleAction}
              togglePopup={this.togglePopup}
              togglePopupLocation={this.togglePopupLocation}
              togglePopupTcpOs={this.togglePopupTcpOs}
              handleImmediateChangeIp={this.handleImmediateChangeIp}
              togglePopupAutoRotation={this.togglePopupAutoRotation}
              togglePopupExtend={this.togglePopupExtend}
              togglePopupManageLicense={this.togglePopupManageLicense}
              isConfirmLoading={isConfirmLoading}
              handleChangeVpn={this.handleConfirmChangeVpn}
              downloadVpn={this.downloadVpn}
            />
          </Card>
        </Fragment>
        <ConfirmDialog
          message={confirmMessage}
          title={intl.formatMessage(messages.titleConfirm)}
          isOpen={isConfirm}
          confirmButtonText={intl.formatMessage(messages.confirmButton)}
          cancelButtonText={intl.formatMessage(messages.cancelButton)}
          onConfirm={this.handleAcceptConfirmPopup}
          onClose={this.handleCloseConfirmPopup}
          focusCloseButton
          loading={isConfirmLoading}
        />
        <AuthenticatePopup
          isOpen={isOpenPopup}
          selectedIds={selectedIds}
          selectedId={selectedId}
          handleOnClose={() => this.togglePopup(false)}
          forceRefresh={this.forceRefresh}
          dataList={dataList}
        />
        <LocationPopup
          isOpen={isOpenPopupLocation}
          selectedIds={selectedIds}
          selectedId={selectedId}
          handleOnClose={() => this.togglePopupLocation(false)}
          forceRefresh={this.forceRefresh}
          locations={locations}
          modemType={modemType}
          dataList={dataList}
        />
        <TcpOsPopup
          isOpen={isOpenPopupTcpOs}
          selectedIds={selectedIds}
          selectedId={selectedId}
          handleOnClose={() => this.togglePopupTcpOs(false)}
          forceRefresh={this.forceRefresh}
        />
        <AutoRotationPopup
          isOpen={isOpenPopupRotation}
          selectedIds={selectedIds}
          selectedId={selectedId}
          autoRotationTime={autoRotationTime}
          minPkgTime={minPkgTime}
          handleOnClose={() => this.togglePopupAutoRotation(false)}
          forceRefresh={this.forceRefresh}
        />
        <ExtendPopup
          isOpen={isOpenPopupExtend}
          selectedIds={selectedIds}
          selectedId={selectedId}
          packageUnit={packageUnit}
          handleOnClose={() => this.togglePopupExtend(false)}
          forceRefresh={this.forceRefresh}
          packages={packages}
          location={locationExt}
        />
        <ManageLicensePopup
          isOpen={isOpenPopupManageLicense}
          handleOnClose={() => this.togglePopupManageLicense(false)}
          selectedIds={selectedIds}
          dataList={dataList}
          packages={packages}
          packageUnit={packageUnit}
          location={locationExt}
          vpnFee={vpnFee}
          forceRefresh={this.fetchProxyListData}
        />
      </StyledContainer>
    );
  }
}

export default compose(
  WithHandlePromise,
  WithHandleAlert,
  injectIntl
)(ProxyWanList);
