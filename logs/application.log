2025-08-24 17:46:33.477 [[background-preinit]] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-08-24 17:46:33.478 [[background-preinit]] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-08-24 17:46:33.481 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Starting NeoProxyApiApplication using Java 17.0.16 on vinhbui-HP-EliteBook-845-14-inch-G10-Notebook-PC with PID 75861 (/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes started by vinhbui8 in /home/<USER>/Documents/aaaproxy/neo-proxy_app)
2025-08-24 17:46:33.482 [[main]] INFO  c.n.pro.NeoProxyApiApplication - The following profiles are active: dev
2025-08-24 17:46:33.897 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-24 17:46:33.969 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 68 ms. Found 14 JPA repository interfaces.
2025-08-24 17:46:34.228 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-24 17:46:34.232 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-24 17:46:34.232 [[main]] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-24 17:46:34.232 [[main]] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-08-24 17:46:34.271 [[main]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring embedded WebApplicationContext
2025-08-24 17:46:34.271 [[main]] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 768 ms
2025-08-24 17:46:34.295 [[main]] DEBUG i.m.c.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-08-24 17:46:34.376 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09

2025-08-24 17:46:34.409 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - AWS SDK available: false
2025-08-24 17:46:34.409 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - Google Cloud Storage available: false
2025-08-24 17:46:34.410 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-08-24 17:46:34.410 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-24 17:46:34.410 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-08-24 17:46:34.410 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-08-24 17:46:34.410 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-24 17:46:34.410 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration
2025-08-24 17:46:34.410 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - JBoss VFS v2 available: false
2025-08-24 17:46:34.411 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/
2025-08-24 17:46:34.411 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration (db/migration)
2025-08-24 17:46:34.411 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql
2025-08-24 17:46:34.411 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql
2025-08-24 17:46:34.411 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql
2025-08-24 17:46:34.411 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__CREATE_TABLE_USERS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V20__UPDATE_TABLE_USER.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V25__UPDATE_TABLE_MAIL.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V2__CREATE_TABLE_MODEMS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V32__CREATE_TABLE_PROXYS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V38__UPDATE_TABLE_MAIL.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V4__CREATE_TABLE_LICENSES.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V7__CREATE_TABLE_MONITORS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql
2025-08-24 17:46:34.412 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql
2025-08-24 17:46:34.413 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-08-24 17:46:34.414 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-08-24 17:46:34.414 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension................................false
2025-08-24 17:46:34.414 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit................................true
2025-08-24 17:46:34.414 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - catalog................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout................................30000
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSource................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties................................{password=<masked>}
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................................"org.postgresql.Driver"
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties................................{}
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout................................***********-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout................................1
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries................................false
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl................................********************************************
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime................................0
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold................................0
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime................................1800000
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize................................10
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory................................none
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle................................10
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - password................................<masked>
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - poolName................................"HikariPool-1"
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - readOnly................................false
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans................................false
2025-08-24 17:46:34.415 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor................................none
2025-08-24 17:46:34.416 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - schema................................none
2025-08-24 17:46:34.416 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory................................internal
2025-08-24 17:46:34.416 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation................................default
2025-08-24 17:46:34.416 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - username................................"admin"
2025-08-24 17:46:34.416 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout................................5000
2025-08-24 17:46:34.416 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-24 17:46:34.530 [[main]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@f59da34
2025-08-24 17:46:34.530 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-24 17:46:34.545 [[main]] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 8.0.5 by Redgate
2025-08-24 17:46:34.545 [[main]] INFO  o.f.c.i.d.base.BaseDatabaseType - Database: ******************************************** (PostgreSQL 16.9)
2025-08-24 17:46:34.545 [[main]] DEBUG o.f.c.i.d.base.BaseDatabaseType - Driver  : PostgreSQL JDBC Driver 42.3.1
2025-08-24 17:46:34.546 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: true
2025-08-24 17:46:34.546 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-08-24 17:46:34.546 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-08-24 17:46:34.550 [[main]] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: PostgreSQL 16.9 is newer than this version of Flyway and support has not been tested. The latest supported version of PostgreSQL is 14.
2025-08-24 17:46:34.550 [[main]] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-08-24 17:46:34.560 [[main]] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V20__UPDATE_TABLE_USER.sql (filename: V20__UPDATE_TABLE_USER.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql (filename: V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql (filename: V11__CREATE_TABLE_TRACKINGS.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V2__CREATE_TABLE_MODEMS.sql (filename: V2__CREATE_TABLE_MODEMS.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V38__UPDATE_TABLE_MAIL.sql (filename: V38__UPDATE_TABLE_MAIL.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V25__UPDATE_TABLE_MAIL.sql (filename: V25__UPDATE_TABLE_MAIL.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql (filename: V6__CREATE_TABLE_TRANSACTIONS.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql (filename: V18__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql (filename: V12__UPDATE_TABLE_TRANSACTIONS.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql (filename: V39__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 17:46:34.569 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V4__CREATE_TABLE_LICENSES.sql (filename: V4__CREATE_TABLE_LICENSES.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql (filename: V22__UPDATE_TABLE_CONFIG.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql (filename: V14__UPDATE_TABLE_PROXYS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql (filename: V8__CREATE_TABLE_REFRESH_TOKENS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql (filename: V23__CREATE_TABLE_MAIL_TEMPLATE.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql (filename: V35__UPDATE_TABLE_PACKAGES.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql (filename: V15__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql (filename: V17__CREATE_TABLE_PROMOTION.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql (filename: V19__UPDATE_TABLE_LICENSES.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__CREATE_TABLE_USERS.sql (filename: V1__CREATE_TABLE_USERS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql (filename: V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql (filename: V16__UPDATE_TABLE_CONFIG.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql (filename: V37__UPDATE_TABLE_LICENSES.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql (filename: V10__CREATE_TABLE_LOCATIONS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql (filename: V33__UPDATE_TABLE_PROXY_FOR_ISP.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql (filename: V5__CREATE_TABLE_PACKAGES.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql (filename: V40__UPDATE_TABLE_PROMOTION.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V32__CREATE_TABLE_PROXYS.sql (filename: V32__CREATE_TABLE_PROXYS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql (filename: V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql (filename: V9__CREATE_TABLE_CONFIGURATIONS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V7__CREATE_TABLE_MONITORS.sql (filename: V7__CREATE_TABLE_MONITORS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql (filename: V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql (filename: V21__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql (filename: V24__UPDATE_TABLE_PROXYS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql (filename: V13__UPDATE_TABLE_PROXYS.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql (filename: V36__UPDATE_TABLE_LICENSES.sql)
2025-08-24 17:46:34.570 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql (filename: V3__CREATE_TABLE_PROXY_WANS.sql)
2025-08-24 17:46:34.581 [[main]] INFO  o.f.core.internal.command.DbValidate - Successfully validated 37 migrations (execution time 00:00.020s)
2025-08-24 17:46:34.587 [[main]] INFO  o.f.core.internal.command.DbMigrate - Current version of schema "public": 42
2025-08-24 17:46:34.587 [[main]] INFO  o.f.core.internal.command.DbMigrate - Schema "public" is up to date. No migration necessary.
2025-08-24 17:46:34.590 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 63 of 136M
2025-08-24 17:46:34.624 [[main]] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-24 17:46:34.631 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-08-24 17:46:34.643 [[main]] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.3.Final
2025-08-24 17:46:34.650 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@58b42519
2025-08-24 17:46:34.667 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4dae847e
2025-08-24 17:46:34.685 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@65d29242
2025-08-24 17:46:34.702 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5c6667f2
2025-08-24 17:46:34.706 [[main]] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-24 17:46:34.719 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@69c84961
2025-08-24 17:46:34.736 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6feea5d
2025-08-24 17:46:34.751 [[main]] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-08-24 17:46:34.753 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5b039bd0
2025-08-24 17:46:34.769 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@febc4f6
2025-08-24 17:46:34.785 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2443072b
2025-08-24 17:46:34.785 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 17:46:35.115 [[main]] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-24 17:46:35.118 [[main]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-24 17:46:35.589 [[main]] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-24 17:46:35.769 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/v1/rh-websocket/**'] with []
2025-08-24 17:46:35.769 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/index.html'] with []
2025-08-24 17:46:35.769 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/app.js'] with []
2025-08-24 17:46:35.769 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/favicon.ico'] with []
2025-08-24 17:46:35.786 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@3fb54673, org.springframework.security.web.context.SecurityContextPersistenceFilter@4a1cafb2, org.springframework.security.web.header.HeaderWriterFilter@52d625bd, org.springframework.security.web.authentication.logout.LogoutFilter@162c89ac, com.neoproxy.pro.config.JwtRequestFilter@18f1631c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@335cd031, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@63fef83c, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@4b55652a, org.springframework.security.web.session.SessionManagementFilter@34986491, org.springframework.security.web.access.ExceptionTranslationFilter@35fddea2, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@56d4481f]
2025-08-24 17:46:35.882 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.AuthenticationController:
	{POST [/v1/users/authentication/email]}: authenticateByEmail(EmailAuthenticationRequest)
2025-08-24 17:46:35.884 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.ISPController:
	{GET [/v1/isp/list]}: getLocations()
2025-08-24 17:46:35.885 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.LocationController:
	{POST [/v1/locations/list]}: getLocations()
	{GET [/v1/locations/list]}: getList()
	{GET [/v1/locations/full-list]}: getFullList()
2025-08-24 17:46:35.885 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PackageController:
	{POST [/v1/packages/list]}: getPackages(PackageQueryRequest)
	{POST [/v1/packages]}: createNewPackage(PackageRequest)
	{PUT [/v1/packages/{uuid}]}: updatePackage(UUID,PackageRequest)
	{DELETE [/v1/packages/{uuid}]}: deletePackage(UUID)
2025-08-24 17:46:35.886 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PingController:
	{GET [/v1/ping]}: ping()
2025-08-24 17:46:35.887 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.UserController:
	{PUT [/v1/users/reset-password]}: resetPassword(ResetPasswordRequest)
	{POST [/v1/users/change-password]}: changePassword(ResetPasswordRequest)
	{GET [/v1/users/verify/{code}]}: verifyAccount(String)
	{POST [/v1/users/register]}: registerNewUser(NewUserRequest)
	{GET [/v1/users/me]}: getLoggedUser()
	{PUT [/v1/users/email]}: registerNewUserByEmail(UserEmailUpdateRequest)
	{GET [/v1/users/change-reminder]}: changeReminder()
2025-08-24 17:46:35.888 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ConfigurationController:
	{PUT [/v1/admin/configurations/{uuid}]}: updateConfiguration(UUID,ConfigurationRequest)
	{POST [/v1/admin/configurations/list]}: getList(ConfigurationQueryRequest)
	{POST [/v1/admin/configurations/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{POST [/v1/admin/configurations/cate]}: getCate(ConfigurationQueryRequest)
	{PUT [/v1/admin/configurations/cate]}: updateListConf(Map)
2025-08-24 17:46:35.889 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.CustomerController:
	{POST [/v1/admin/customers/{uuid}/active]}: active(UUID)
	{POST [/v1/admin/customers/list]}: getList(CustomerQueryRequest)
	{GET [/v1/admin/customers/{uuid}]}: resetPassword(UUID)
	{POST [/v1/admin/customers/{uuid}/reset-password/{new-password}]}: resetPassword(UUID,String)
	{POST [/v1/admin/customers/{uuid}/topup/{amount}]}: topup(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/refund/{amount}]}: refund(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/suspended]}: suspended(UUID)
	{ [/v1/admin/customers/excel]}: excel(HttpServletResponse)
2025-08-24 17:46:35.890 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.LicenseController:
	{ [/v1/admin/licenses/tracking]}: excel(HttpServletResponse,UUID)
	{POST [/v1/admin/licenses/list]}: getLicenses(LicenseQueryRequest,HttpServletRequest)
	{PUT [/v1/admin/licenses/{uuid}]}: updateLicense(UUID,LicenseRequest)
	{POST [/v1/admin/licenses/update-license]}: updateLicenseStatus(UpdateLicenseStatusRequest)
	{POST [/v1/admin/licenses/switch-modem]}: switchNewModem(SwitchModemRequest)
	{POST [/v1/admin/licenses/change-proxy]}: changeAvailablePort(LicenseChangeProxyRequest)
2025-08-24 17:46:35.892 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MailTemplateController:
	{POST [/v1/admin/mail-templates/list]}: getList(MailTemplateQueryRequest)
	{POST [/v1/admin/mail-templates/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{PUT [/v1/admin/mail-templates/{uuid}]}: updateMailTemplate(UUID,MailTemplateRequest)
	{POST [/v1/admin/mail-templates]}: insertMailTemplate(MailTemplateRequest)
2025-08-24 17:46:35.894 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ModemController:
	{DELETE [/v1/admin/modems/{uuid}]}: delete(UUID)
	{GET [/v1/admin/modems/{uuid}/resume]}: resume(UUID)
	{GET [/v1/admin/modems/{uuid}/sync]}: sync(UUID)
	{GET [/v1/admin/modems/{uuid}]}: detail(UUID)
	{POST [/v1/admin/modems]}: createNewModem(ModemRequest)
	{PUT [/v1/admin/modems/{uuid}]}: updateModem(UUID,ModemRequest)
	{POST [/v1/admin/modems/list]}: getModems(ModemQueryRequest)
	{GET [/v1/admin/modems/{uuid}/pause]}: pause(UUID)
	{POST [/v1/admin/modems/generate-port]}: generatePort(GeneratePortRequest)
2025-08-24 17:46:35.894 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MonitorController:
	{POST [/v1/admin/monitors/list]}: getList(MonitorQueryRequest)
2025-08-24 17:46:35.894 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.OverviewController:
	{GET [/v1/admin/overview]}: overview()
2025-08-24 17:46:35.894 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.PromotionController:
	{POST [/v1/promotions]}: createNewPromotion(PromotionRequest)
	{PUT [/v1/promotions/{uuid}]}: updatePromotion(UUID,PromotionRequest)
	{POST [/v1/promotions/list]}: getPromotions(PromotionQueryRequest)
	{DELETE [/v1/promotions/{uuid}]}: deletePromotion(UUID)
2025-08-24 17:46:35.895 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ProxyController:
	{DELETE [/v1/admin/proxies/delete]}: deleteProxies(ProxyRequest)
	{POST [/v1/admin/proxies/list]}: getProxyWans(ProxyQueryRequest)
2025-08-24 17:46:35.895 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.TransactionController:
	{POST [/v1/admin/transactions/list]}: getList(TransactionQueryRequest)
2025-08-24 17:46:35.895 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ApiController:
	{GET [/v1/api/proxy/status]}: getStatus(String)
	{GET [/v1/api/proxy/change-ip]}: changeIp(String)
	{GET [/v1/api/proxy/change-rotation-time]}: changeRotationTime(String,Integer)
2025-08-24 17:46:35.896 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientLicenseController:
	{ [/v1/client/licenses/excel]}: excel(HttpServletResponse,String)
	{POST [/v1/client/licenses/change-rotation-time]}: changeRotationTime(ChangeRotationTimeRequest)
	{POST [/v1/client/licenses/toggle-auto-renewal]}: toggleAutoRenewal(ToggleAutoRenewalRequest)
	{POST [/v1/client/licenses/purchase-vpn]}: purchaseVpn(PurchaseVpnRequest)
	{POST [/v1/client/licenses/cancel-vpn]}: cancelVpn(CancelVpnRequest)
	{POST [/v1/client/licenses/list]}: getClientLicenses(LicenseQueryRequest)
	{POST [/v1/client/licenses/extend]}: renewIp(ExtendLicenseRequest)
	{POST [/v1/client/licenses/express-extend]}: expressExtend(ExpressExtendLicenseRequest)
	{POST [/v1/client/licenses/update-tcp-os]}: updateTcpOS(UpdateTcpOSRequest)
2025-08-24 17:46:35.896 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPackageController:
	{POST [/v1/client/packages/list]}: getPackages(PackageQueryRequest)
2025-08-24 17:46:35.897 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPromotionController:
	{GET [/v1/client/promotions/redeem-code]}: redeemCode(String,BigDecimal)
	{POST [/v1/client/promotions/quantity]}: getPromotionQuantity(PromotionQueryRequest)
	{GET [/v1/client/promotions/discount]}: getDiscountByPromotion(String,BigDecimal)
2025-08-24 17:46:35.897 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientProxyController:
	{POST [/v1/client/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{POST [/v1/client/proxies/reboot-device]}: rebootDevice(ProxyRequest)
	{POST [/v1/client/proxies/change-location]}: changeLocation(ProxyRequest)
	{POST [/v1/client/proxies/update-authenticate]}: updateAuthenticate(ProxyRequest)
	{POST [/v1/client/proxies/extend-license]}: extendLicense(ExtendLicenseRequest)
	{POST [/v1/client/proxies/change-ip]}: changeProxyIp(ProxyRequest)
2025-08-24 17:46:35.898 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionController:
	{POST [/v1/client/transactions/list]}: getList(TransactionQueryRequest)
	{POST [/v1/client/transactions/place-order]}: createNewOrder(OrderProxyRequest)
2025-08-24 17:46:35.898 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionV2Controller:
	{POST [/v1/client/orders/place-order]}: checkOrder(OrderProxyV2Request)
2025-08-24 17:46:35.898 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.PaymentController:
	{GET [/v1/client/payments/minimum-amount]}: getMinimumAmount(String)
	{POST [/v1/client/payments/topup]}: topup(TopupRequest)
	{GET [/v1/client/payments/full-currencies]}: getFullCurrencies()
	{GET [/v1/client/payments/estimated-price]}: getEstimatedPrice(String,String,BigDecimal)
	{POST [/v1/client/payments/webhook]}: webhook(String,String)
	{GET [/v1/client/payments/currencies]}: getCurrencies()
	{GET [/v1/client/payments/get-payment-url]}: getPaymentUrl(BigDecimal)
2025-08-24 17:46:35.898 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.VpnController:
	{POST [/v1/client/vpn/change]}: change(VpnRequest)
	{GET [/v1/client/vpn/download]}: downloadFile(String)
2025-08-24 17:46:35.900 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-08-24 17:46:35.906 [[main]] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-24 17:46:35.915 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-08-24 17:46:35.925 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-08-24 17:46:36.058 [[main]] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-08-24 17:46:36.081 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-24 17:46:36.087 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/neoproxy'
2025-08-24 17:46:36.097 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Started NeoProxyApiApplication in 2.785 seconds (JVM running for 3.078)
2025-08-24 17:47:04.631 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 17:47:04.632 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 17:47:06.347 [[SpringApplicationShutdownHook]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-24 17:47:06.350 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-24 17:47:06.350 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Before shutdown stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 17:47:06.351 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@f59da34: (connection evicted)
2025-08-24 17:47:06.352 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@58b42519: (connection evicted)
2025-08-24 17:47:06.352 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@4dae847e: (connection evicted)
2025-08-24 17:47:06.353 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@65d29242: (connection evicted)
2025-08-24 17:47:06.353 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@5c6667f2: (connection evicted)
2025-08-24 17:47:06.354 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@69c84961: (connection evicted)
2025-08-24 17:47:06.354 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@6feea5d: (connection evicted)
2025-08-24 17:47:06.354 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@5b039bd0: (connection evicted)
2025-08-24 17:47:06.355 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@febc4f6: (connection evicted)
2025-08-24 17:47:06.355 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@2443072b: (connection evicted)
2025-08-24 17:47:06.355 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-08-24 17:47:06.356 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-24 17:47:18.902 [[background-preinit]] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-08-24 17:47:18.904 [[background-preinit]] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-08-24 17:47:18.907 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Starting NeoProxyApiApplication using Java 17.0.16 on vinhbui-HP-EliteBook-845-14-inch-G10-Notebook-PC with PID 76337 (/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes started by vinhbui8 in /home/<USER>/Documents/aaaproxy/neo-proxy_app)
2025-08-24 17:47:18.907 [[main]] INFO  c.n.pro.NeoProxyApiApplication - The following profiles are active: dev
2025-08-24 17:47:19.370 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-24 17:47:19.437 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 63 ms. Found 14 JPA repository interfaces.
2025-08-24 17:47:19.769 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-24 17:47:19.774 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-24 17:47:19.774 [[main]] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-24 17:47:19.774 [[main]] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-08-24 17:47:19.815 [[main]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring embedded WebApplicationContext
2025-08-24 17:47:19.815 [[main]] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 878 ms
2025-08-24 17:47:19.843 [[main]] DEBUG i.m.c.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-08-24 17:47:19.921 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09

2025-08-24 17:47:19.954 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - AWS SDK available: false
2025-08-24 17:47:19.954 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - Google Cloud Storage available: false
2025-08-24 17:47:19.954 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-08-24 17:47:19.954 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-24 17:47:19.954 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-08-24 17:47:19.954 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-08-24 17:47:19.954 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-24 17:47:19.954 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration
2025-08-24 17:47:19.955 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - JBoss VFS v2 available: false
2025-08-24 17:47:19.955 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/
2025-08-24 17:47:19.955 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration (db/migration)
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__CREATE_TABLE_USERS.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V20__UPDATE_TABLE_USER.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql
2025-08-24 17:47:19.956 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V25__UPDATE_TABLE_MAIL.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V2__CREATE_TABLE_MODEMS.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V32__CREATE_TABLE_PROXYS.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V38__UPDATE_TABLE_MAIL.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V4__CREATE_TABLE_LICENSES.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V7__CREATE_TABLE_MONITORS.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql
2025-08-24 17:47:19.957 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-08-24 17:47:19.958 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension................................false
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit................................true
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - catalog................................none
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql................................none
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery................................none
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout................................30000
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSource................................none
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName................................none
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI................................none
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties................................{password=<masked>}
2025-08-24 17:47:19.959 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................................"org.postgresql.Driver"
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName................................none
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties................................{}
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry................................none
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout................................***********-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout................................1
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries................................false
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl................................********************************************
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime................................0
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold................................0
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime................................1800000
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize................................10
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry................................none
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory................................none
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle................................10
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - password................................<masked>
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - poolName................................"HikariPool-1"
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - readOnly................................false
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans................................false
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor................................none
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - schema................................none
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory................................internal
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation................................default
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - username................................"admin"
2025-08-24 17:47:19.960 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout................................5000
2025-08-24 17:47:19.960 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-24 17:47:20.062 [[main]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@1377b1a0
2025-08-24 17:47:20.063 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-24 17:47:20.074 [[main]] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 8.0.5 by Redgate
2025-08-24 17:47:20.074 [[main]] INFO  o.f.c.i.d.base.BaseDatabaseType - Database: ******************************************** (PostgreSQL 16.9)
2025-08-24 17:47:20.074 [[main]] DEBUG o.f.c.i.d.base.BaseDatabaseType - Driver  : PostgreSQL JDBC Driver 42.3.1
2025-08-24 17:47:20.075 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: true
2025-08-24 17:47:20.076 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-08-24 17:47:20.076 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-08-24 17:47:20.080 [[main]] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: PostgreSQL 16.9 is newer than this version of Flyway and support has not been tested. The latest supported version of PostgreSQL is 14.
2025-08-24 17:47:20.080 [[main]] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-08-24 17:47:20.085 [[main]] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V20__UPDATE_TABLE_USER.sql (filename: V20__UPDATE_TABLE_USER.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql (filename: V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql (filename: V11__CREATE_TABLE_TRACKINGS.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V2__CREATE_TABLE_MODEMS.sql (filename: V2__CREATE_TABLE_MODEMS.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V38__UPDATE_TABLE_MAIL.sql (filename: V38__UPDATE_TABLE_MAIL.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V25__UPDATE_TABLE_MAIL.sql (filename: V25__UPDATE_TABLE_MAIL.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql (filename: V6__CREATE_TABLE_TRANSACTIONS.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql (filename: V18__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql (filename: V12__UPDATE_TABLE_TRANSACTIONS.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql (filename: V39__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V4__CREATE_TABLE_LICENSES.sql (filename: V4__CREATE_TABLE_LICENSES.sql)
2025-08-24 17:47:20.095 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql (filename: V22__UPDATE_TABLE_CONFIG.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql (filename: V14__UPDATE_TABLE_PROXYS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql (filename: V8__CREATE_TABLE_REFRESH_TOKENS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql (filename: V23__CREATE_TABLE_MAIL_TEMPLATE.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql (filename: V35__UPDATE_TABLE_PACKAGES.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql (filename: V15__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql (filename: V17__CREATE_TABLE_PROMOTION.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql (filename: V19__UPDATE_TABLE_LICENSES.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__CREATE_TABLE_USERS.sql (filename: V1__CREATE_TABLE_USERS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql (filename: V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql (filename: V16__UPDATE_TABLE_CONFIG.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql (filename: V37__UPDATE_TABLE_LICENSES.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql (filename: V10__CREATE_TABLE_LOCATIONS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql (filename: V33__UPDATE_TABLE_PROXY_FOR_ISP.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql (filename: V5__CREATE_TABLE_PACKAGES.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql (filename: V40__UPDATE_TABLE_PROMOTION.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V32__CREATE_TABLE_PROXYS.sql (filename: V32__CREATE_TABLE_PROXYS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql (filename: V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql (filename: V9__CREATE_TABLE_CONFIGURATIONS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V7__CREATE_TABLE_MONITORS.sql (filename: V7__CREATE_TABLE_MONITORS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql (filename: V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql (filename: V21__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql (filename: V24__UPDATE_TABLE_PROXYS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql (filename: V13__UPDATE_TABLE_PROXYS.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql (filename: V36__UPDATE_TABLE_LICENSES.sql)
2025-08-24 17:47:20.096 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql (filename: V3__CREATE_TABLE_PROXY_WANS.sql)
2025-08-24 17:47:20.105 [[main]] INFO  o.f.core.internal.command.DbValidate - Successfully validated 37 migrations (execution time 00:00.019s)
2025-08-24 17:47:20.110 [[main]] INFO  o.f.core.internal.command.DbMigrate - Current version of schema "public": 42
2025-08-24 17:47:20.110 [[main]] INFO  o.f.core.internal.command.DbMigrate - Schema "public" is up to date. No migration necessary.
2025-08-24 17:47:20.113 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 63 of 148M
2025-08-24 17:47:20.149 [[main]] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-24 17:47:20.163 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-08-24 17:47:20.168 [[main]] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.3.Final
2025-08-24 17:47:20.183 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@38fc5ec4
2025-08-24 17:47:20.201 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4ab59275
2025-08-24 17:47:20.219 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@41fb82f5
2025-08-24 17:47:20.229 [[main]] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-24 17:47:20.237 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@273b8685
2025-08-24 17:47:20.254 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2945f83
2025-08-24 17:47:20.270 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7960eefa
2025-08-24 17:47:20.274 [[main]] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-08-24 17:47:20.287 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3fb71e73
2025-08-24 17:47:20.304 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@c34333f
2025-08-24 17:47:20.320 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2c7c1d7d
2025-08-24 17:47:20.321 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 17:47:20.642 [[main]] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-24 17:47:20.646 [[main]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-24 17:47:21.122 [[main]] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-24 17:47:21.296 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/v1/rh-websocket/**'] with []
2025-08-24 17:47:21.296 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/index.html'] with []
2025-08-24 17:47:21.296 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/app.js'] with []
2025-08-24 17:47:21.296 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/favicon.ico'] with []
2025-08-24 17:47:21.312 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@5d5a77de, org.springframework.security.web.context.SecurityContextPersistenceFilter@6971c6b7, org.springframework.security.web.header.HeaderWriterFilter@59d8a968, org.springframework.security.web.authentication.logout.LogoutFilter@651a399, com.neoproxy.pro.config.JwtRequestFilter@18f1631c, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@4b37b01e, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2582e213, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@460a4935, org.springframework.security.web.session.SessionManagementFilter@2cb4e4b1, org.springframework.security.web.access.ExceptionTranslationFilter@225d93aa, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@4f3a8e3e]
2025-08-24 17:47:21.401 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.AuthenticationController:
	{POST [/v1/users/authentication/email]}: authenticateByEmail(EmailAuthenticationRequest)
2025-08-24 17:47:21.402 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.ISPController:
	{GET [/v1/isp/list]}: getLocations()
2025-08-24 17:47:21.403 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.LocationController:
	{POST [/v1/locations/list]}: getLocations()
	{GET [/v1/locations/list]}: getList()
	{GET [/v1/locations/full-list]}: getFullList()
2025-08-24 17:47:21.404 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PackageController:
	{POST [/v1/packages/list]}: getPackages(PackageQueryRequest)
	{POST [/v1/packages]}: createNewPackage(PackageRequest)
	{PUT [/v1/packages/{uuid}]}: updatePackage(UUID,PackageRequest)
	{DELETE [/v1/packages/{uuid}]}: deletePackage(UUID)
2025-08-24 17:47:21.404 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PingController:
	{GET [/v1/ping]}: ping()
2025-08-24 17:47:21.405 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.UserController:
	{PUT [/v1/users/reset-password]}: resetPassword(ResetPasswordRequest)
	{POST [/v1/users/change-password]}: changePassword(ResetPasswordRequest)
	{GET [/v1/users/verify/{code}]}: verifyAccount(String)
	{POST [/v1/users/register]}: registerNewUser(NewUserRequest)
	{GET [/v1/users/me]}: getLoggedUser()
	{PUT [/v1/users/email]}: registerNewUserByEmail(UserEmailUpdateRequest)
	{GET [/v1/users/change-reminder]}: changeReminder()
2025-08-24 17:47:21.406 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ConfigurationController:
	{PUT [/v1/admin/configurations/{uuid}]}: updateConfiguration(UUID,ConfigurationRequest)
	{POST [/v1/admin/configurations/list]}: getList(ConfigurationQueryRequest)
	{POST [/v1/admin/configurations/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{POST [/v1/admin/configurations/cate]}: getCate(ConfigurationQueryRequest)
	{PUT [/v1/admin/configurations/cate]}: updateListConf(Map)
2025-08-24 17:47:21.407 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.CustomerController:
	{POST [/v1/admin/customers/{uuid}/active]}: active(UUID)
	{POST [/v1/admin/customers/list]}: getList(CustomerQueryRequest)
	{GET [/v1/admin/customers/{uuid}]}: resetPassword(UUID)
	{POST [/v1/admin/customers/{uuid}/reset-password/{new-password}]}: resetPassword(UUID,String)
	{POST [/v1/admin/customers/{uuid}/topup/{amount}]}: topup(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/refund/{amount}]}: refund(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/suspended]}: suspended(UUID)
	{ [/v1/admin/customers/excel]}: excel(HttpServletResponse)
2025-08-24 17:47:21.408 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.LicenseController:
	{ [/v1/admin/licenses/tracking]}: excel(HttpServletResponse,UUID)
	{POST [/v1/admin/licenses/list]}: getLicenses(LicenseQueryRequest,HttpServletRequest)
	{PUT [/v1/admin/licenses/{uuid}]}: updateLicense(UUID,LicenseRequest)
	{POST [/v1/admin/licenses/update-license]}: updateLicenseStatus(UpdateLicenseStatusRequest)
	{POST [/v1/admin/licenses/switch-modem]}: switchNewModem(SwitchModemRequest)
	{POST [/v1/admin/licenses/change-proxy]}: changeAvailablePort(LicenseChangeProxyRequest)
2025-08-24 17:47:21.409 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MailTemplateController:
	{POST [/v1/admin/mail-templates/list]}: getList(MailTemplateQueryRequest)
	{POST [/v1/admin/mail-templates/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{PUT [/v1/admin/mail-templates/{uuid}]}: updateMailTemplate(UUID,MailTemplateRequest)
	{POST [/v1/admin/mail-templates]}: insertMailTemplate(MailTemplateRequest)
2025-08-24 17:47:21.411 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ModemController:
	{DELETE [/v1/admin/modems/{uuid}]}: delete(UUID)
	{GET [/v1/admin/modems/{uuid}/resume]}: resume(UUID)
	{GET [/v1/admin/modems/{uuid}/sync]}: sync(UUID)
	{GET [/v1/admin/modems/{uuid}]}: detail(UUID)
	{POST [/v1/admin/modems]}: createNewModem(ModemRequest)
	{PUT [/v1/admin/modems/{uuid}]}: updateModem(UUID,ModemRequest)
	{POST [/v1/admin/modems/list]}: getModems(ModemQueryRequest)
	{GET [/v1/admin/modems/{uuid}/pause]}: pause(UUID)
	{POST [/v1/admin/modems/generate-port]}: generatePort(GeneratePortRequest)
2025-08-24 17:47:21.412 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MonitorController:
	{POST [/v1/admin/monitors/list]}: getList(MonitorQueryRequest)
2025-08-24 17:47:21.412 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.OverviewController:
	{GET [/v1/admin/overview]}: overview()
2025-08-24 17:47:21.412 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.PromotionController:
	{POST [/v1/promotions]}: createNewPromotion(PromotionRequest)
	{PUT [/v1/promotions/{uuid}]}: updatePromotion(UUID,PromotionRequest)
	{POST [/v1/promotions/list]}: getPromotions(PromotionQueryRequest)
	{DELETE [/v1/promotions/{uuid}]}: deletePromotion(UUID)
2025-08-24 17:47:21.412 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ProxyController:
	{DELETE [/v1/admin/proxies/delete]}: deleteProxies(ProxyRequest)
	{POST [/v1/admin/proxies/list]}: getProxyWans(ProxyQueryRequest)
2025-08-24 17:47:21.412 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.TransactionController:
	{POST [/v1/admin/transactions/list]}: getList(TransactionQueryRequest)
2025-08-24 17:47:21.413 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ApiController:
	{GET [/v1/api/proxy/status]}: getStatus(String)
	{GET [/v1/api/proxy/change-ip]}: changeIp(String)
	{GET [/v1/api/proxy/change-rotation-time]}: changeRotationTime(String,Integer)
2025-08-24 17:47:21.414 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientLicenseController:
	{ [/v1/client/licenses/excel]}: excel(HttpServletResponse,String)
	{POST [/v1/client/licenses/change-rotation-time]}: changeRotationTime(ChangeRotationTimeRequest)
	{POST [/v1/client/licenses/toggle-auto-renewal]}: toggleAutoRenewal(ToggleAutoRenewalRequest)
	{POST [/v1/client/licenses/purchase-vpn]}: purchaseVpn(PurchaseVpnRequest)
	{POST [/v1/client/licenses/cancel-vpn]}: cancelVpn(CancelVpnRequest)
	{POST [/v1/client/licenses/list]}: getClientLicenses(LicenseQueryRequest)
	{POST [/v1/client/licenses/extend]}: renewIp(ExtendLicenseRequest)
	{POST [/v1/client/licenses/express-extend]}: expressExtend(ExpressExtendLicenseRequest)
	{POST [/v1/client/licenses/update-tcp-os]}: updateTcpOS(UpdateTcpOSRequest)
2025-08-24 17:47:21.414 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPackageController:
	{POST [/v1/client/packages/list]}: getPackages(PackageQueryRequest)
2025-08-24 17:47:21.414 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPromotionController:
	{GET [/v1/client/promotions/redeem-code]}: redeemCode(String,BigDecimal)
	{POST [/v1/client/promotions/quantity]}: getPromotionQuantity(PromotionQueryRequest)
	{GET [/v1/client/promotions/discount]}: getDiscountByPromotion(String,BigDecimal)
2025-08-24 17:47:21.414 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientProxyController:
	{POST [/v1/client/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{POST [/v1/client/proxies/reboot-device]}: rebootDevice(ProxyRequest)
	{POST [/v1/client/proxies/change-location]}: changeLocation(ProxyRequest)
	{POST [/v1/client/proxies/update-authenticate]}: updateAuthenticate(ProxyRequest)
	{POST [/v1/client/proxies/extend-license]}: extendLicense(ExtendLicenseRequest)
	{POST [/v1/client/proxies/change-ip]}: changeProxyIp(ProxyRequest)
2025-08-24 17:47:21.415 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionController:
	{POST [/v1/client/transactions/list]}: getList(TransactionQueryRequest)
	{POST [/v1/client/transactions/place-order]}: createNewOrder(OrderProxyRequest)
2025-08-24 17:47:21.415 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionV2Controller:
	{POST [/v1/client/orders/place-order]}: checkOrder(OrderProxyV2Request)
2025-08-24 17:47:21.415 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.PaymentController:
	{GET [/v1/client/payments/minimum-amount]}: getMinimumAmount(String)
	{POST [/v1/client/payments/topup]}: topup(TopupRequest)
	{GET [/v1/client/payments/full-currencies]}: getFullCurrencies()
	{GET [/v1/client/payments/estimated-price]}: getEstimatedPrice(String,String,BigDecimal)
	{POST [/v1/client/payments/webhook]}: webhook(String,String)
	{GET [/v1/client/payments/currencies]}: getCurrencies()
	{GET [/v1/client/payments/get-payment-url]}: getPaymentUrl(BigDecimal)
2025-08-24 17:47:21.416 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.VpnController:
	{POST [/v1/client/vpn/change]}: change(VpnRequest)
	{GET [/v1/client/vpn/download]}: downloadFile(String)
2025-08-24 17:47:21.417 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-08-24 17:47:21.422 [[main]] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-24 17:47:21.431 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-08-24 17:47:21.441 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-08-24 17:47:21.560 [[main]] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-08-24 17:47:21.581 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-24 17:47:21.587 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/neoproxy'
2025-08-24 17:47:21.597 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Started NeoProxyApiApplication in 2.89 seconds (JVM running for 3.251)
2025-08-24 17:47:24.501 [[SpringApplicationShutdownHook]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-24 17:47:24.504 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-24 17:47:24.504 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Before shutdown stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 17:47:24.505 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@1377b1a0: (connection evicted)
2025-08-24 17:47:24.507 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@38fc5ec4: (connection evicted)
2025-08-24 17:47:24.507 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@4ab59275: (connection evicted)
2025-08-24 17:47:24.507 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@41fb82f5: (connection evicted)
2025-08-24 17:47:24.508 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@273b8685: (connection evicted)
2025-08-24 17:47:24.508 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@2945f83: (connection evicted)
2025-08-24 17:47:24.508 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7960eefa: (connection evicted)
2025-08-24 17:47:24.508 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@3fb71e73: (connection evicted)
2025-08-24 17:47:24.509 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@c34333f: (connection evicted)
2025-08-24 17:47:24.509 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@2c7c1d7d: (connection evicted)
2025-08-24 17:47:24.509 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-08-24 17:47:24.510 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-24 20:51:49.371 [[background-preinit]] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-08-24 20:51:49.372 [[background-preinit]] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-08-24 20:51:49.375 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Starting NeoProxyApiApplication using Java 17.0.16 on vinhbui-HP-EliteBook-845-14-inch-G10-Notebook-PC with PID 82617 (/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes started by vinhbui8 in /home/<USER>/Documents/aaaproxy/neo-proxy_app)
2025-08-24 20:51:49.375 [[main]] INFO  c.n.pro.NeoProxyApiApplication - The following profiles are active: dev
2025-08-24 20:51:49.767 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-24 20:51:49.826 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 55 ms. Found 14 JPA repository interfaces.
2025-08-24 20:51:50.086 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-24 20:51:50.090 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-24 20:51:50.090 [[main]] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-24 20:51:50.090 [[main]] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-08-24 20:51:50.124 [[main]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring embedded WebApplicationContext
2025-08-24 20:51:50.124 [[main]] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 729 ms
2025-08-24 20:51:50.151 [[main]] DEBUG i.m.c.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-08-24 20:51:50.230 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09

2025-08-24 20:51:50.265 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - AWS SDK available: false
2025-08-24 20:51:50.265 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - Google Cloud Storage available: false
2025-08-24 20:51:50.265 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-08-24 20:51:50.265 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-24 20:51:50.265 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-08-24 20:51:50.265 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-08-24 20:51:50.265 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-24 20:51:50.265 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration
2025-08-24 20:51:50.266 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - JBoss VFS v2 available: false
2025-08-24 20:51:50.266 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/
2025-08-24 20:51:50.266 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration (db/migration)
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__CREATE_TABLE_USERS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V20__UPDATE_TABLE_USER.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V25__UPDATE_TABLE_MAIL.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V2__CREATE_TABLE_MODEMS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V32__CREATE_TABLE_PROXYS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V38__UPDATE_TABLE_MAIL.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V4__CREATE_TABLE_LICENSES.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql
2025-08-24 20:51:50.267 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql
2025-08-24 20:51:50.268 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V7__CREATE_TABLE_MONITORS.sql
2025-08-24 20:51:50.268 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql
2025-08-24 20:51:50.268 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql
2025-08-24 20:51:50.268 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-08-24 20:51:50.268 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension................................false
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit................................true
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - catalog................................none
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql................................none
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery................................none
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout................................30000
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSource................................none
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName................................none
2025-08-24 20:51:50.269 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI................................none
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties................................{password=<masked>}
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................................"org.postgresql.Driver"
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName................................none
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties................................{}
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry................................none
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout................................***********-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout................................1
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries................................false
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl................................********************************************
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime................................0
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold................................0
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime................................1800000
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize................................10
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry................................none
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory................................none
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle................................10
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - password................................<masked>
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - poolName................................"HikariPool-1"
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - readOnly................................false
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans................................false
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor................................none
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - schema................................none
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory................................internal
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation................................default
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - username................................"admin"
2025-08-24 20:51:50.270 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout................................5000
2025-08-24 20:51:50.270 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-24 20:51:50.367 [[main]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@27dbaa33
2025-08-24 20:51:50.368 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-24 20:51:50.378 [[main]] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 8.0.5 by Redgate
2025-08-24 20:51:50.379 [[main]] INFO  o.f.c.i.d.base.BaseDatabaseType - Database: ******************************************** (PostgreSQL 16.9)
2025-08-24 20:51:50.379 [[main]] DEBUG o.f.c.i.d.base.BaseDatabaseType - Driver  : PostgreSQL JDBC Driver 42.3.1
2025-08-24 20:51:50.380 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: true
2025-08-24 20:51:50.381 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-08-24 20:51:50.381 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-08-24 20:51:50.384 [[main]] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: PostgreSQL 16.9 is newer than this version of Flyway and support has not been tested. The latest supported version of PostgreSQL is 14.
2025-08-24 20:51:50.385 [[main]] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-08-24 20:51:50.391 [[main]] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V20__UPDATE_TABLE_USER.sql (filename: V20__UPDATE_TABLE_USER.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql (filename: V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql (filename: V11__CREATE_TABLE_TRACKINGS.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V2__CREATE_TABLE_MODEMS.sql (filename: V2__CREATE_TABLE_MODEMS.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V38__UPDATE_TABLE_MAIL.sql (filename: V38__UPDATE_TABLE_MAIL.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V25__UPDATE_TABLE_MAIL.sql (filename: V25__UPDATE_TABLE_MAIL.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql (filename: V6__CREATE_TABLE_TRANSACTIONS.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql (filename: V18__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql (filename: V12__UPDATE_TABLE_TRANSACTIONS.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql (filename: V39__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V4__CREATE_TABLE_LICENSES.sql (filename: V4__CREATE_TABLE_LICENSES.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql (filename: V22__UPDATE_TABLE_CONFIG.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql (filename: V14__UPDATE_TABLE_PROXYS.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql (filename: V8__CREATE_TABLE_REFRESH_TOKENS.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql (filename: V23__CREATE_TABLE_MAIL_TEMPLATE.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql (filename: V35__UPDATE_TABLE_PACKAGES.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql (filename: V15__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql (filename: V17__CREATE_TABLE_PROMOTION.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql (filename: V19__UPDATE_TABLE_LICENSES.sql)
2025-08-24 20:51:50.401 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__CREATE_TABLE_USERS.sql (filename: V1__CREATE_TABLE_USERS.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql (filename: V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql (filename: V16__UPDATE_TABLE_CONFIG.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql (filename: V37__UPDATE_TABLE_LICENSES.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql (filename: V10__CREATE_TABLE_LOCATIONS.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql (filename: V33__UPDATE_TABLE_PROXY_FOR_ISP.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql (filename: V5__CREATE_TABLE_PACKAGES.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql (filename: V40__UPDATE_TABLE_PROMOTION.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V32__CREATE_TABLE_PROXYS.sql (filename: V32__CREATE_TABLE_PROXYS.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql (filename: V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql (filename: V9__CREATE_TABLE_CONFIGURATIONS.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V7__CREATE_TABLE_MONITORS.sql (filename: V7__CREATE_TABLE_MONITORS.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql (filename: V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql (filename: V21__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql (filename: V24__UPDATE_TABLE_PROXYS.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql (filename: V13__UPDATE_TABLE_PROXYS.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql (filename: V36__UPDATE_TABLE_LICENSES.sql)
2025-08-24 20:51:50.402 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql (filename: V3__CREATE_TABLE_PROXY_WANS.sql)
2025-08-24 20:51:50.410 [[main]] INFO  o.f.core.internal.command.DbValidate - Successfully validated 37 migrations (execution time 00:00.018s)
2025-08-24 20:51:50.415 [[main]] INFO  o.f.core.internal.command.DbMigrate - Current version of schema "public": 42
2025-08-24 20:51:50.415 [[main]] INFO  o.f.core.internal.command.DbMigrate - Schema "public" is up to date. No migration necessary.
2025-08-24 20:51:50.419 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 63 of 136M
2025-08-24 20:51:50.455 [[main]] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-24 20:51:50.468 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-08-24 20:51:50.472 [[main]] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.3.Final
2025-08-24 20:51:50.487 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@10ef744a
2025-08-24 20:51:50.504 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@463c1c15
2025-08-24 20:51:50.521 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7c0889d2
2025-08-24 20:51:50.531 [[main]] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-24 20:51:50.537 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5af2fef2
2025-08-24 20:51:50.553 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@2b9e5469
2025-08-24 20:51:50.569 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@66d62a86
2025-08-24 20:51:50.574 [[main]] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-08-24 20:51:50.585 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@71734557
2025-08-24 20:51:50.600 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@624e0cce
2025-08-24 20:51:50.617 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@3b2a31f2
2025-08-24 20:51:50.617 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:51:50.930 [[main]] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-24 20:51:50.933 [[main]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-24 20:51:51.407 [[main]] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-24 20:51:51.576 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/v1/rh-websocket/**'] with []
2025-08-24 20:51:51.576 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/index.html'] with []
2025-08-24 20:51:51.576 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/app.js'] with []
2025-08-24 20:51:51.576 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/favicon.ico'] with []
2025-08-24 20:51:51.592 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@64ee9290, org.springframework.security.web.context.SecurityContextPersistenceFilter@52c919f3, org.springframework.security.web.header.HeaderWriterFilter@34e700f4, org.springframework.security.web.authentication.logout.LogoutFilter@73983f07, com.neoproxy.pro.config.JwtRequestFilter@602d5417, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@77ac7cd6, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@4d7f3adf, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@17e7cc67, org.springframework.security.web.session.SessionManagementFilter@505f6511, org.springframework.security.web.access.ExceptionTranslationFilter@3fe33c59, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@3638a852]
2025-08-24 20:51:51.681 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.AuthenticationController:
	{POST [/v1/users/authentication/email]}: authenticateByEmail(EmailAuthenticationRequest)
2025-08-24 20:51:51.683 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.ISPController:
	{GET [/v1/isp/list]}: getLocations()
2025-08-24 20:51:51.683 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.LocationController:
	{POST [/v1/locations/list]}: getLocations()
	{GET [/v1/locations/list]}: getList()
	{GET [/v1/locations/full-list]}: getFullList()
2025-08-24 20:51:51.684 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PackageController:
	{POST [/v1/packages/list]}: getPackages(PackageQueryRequest)
	{POST [/v1/packages]}: createNewPackage(PackageRequest)
	{PUT [/v1/packages/{uuid}]}: updatePackage(UUID,PackageRequest)
	{DELETE [/v1/packages/{uuid}]}: deletePackage(UUID)
2025-08-24 20:51:51.684 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PingController:
	{GET [/v1/ping]}: ping()
2025-08-24 20:51:51.686 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.UserController:
	{PUT [/v1/users/reset-password]}: resetPassword(ResetPasswordRequest)
	{POST [/v1/users/change-password]}: changePassword(ResetPasswordRequest)
	{GET [/v1/users/verify/{code}]}: verifyAccount(String)
	{POST [/v1/users/register]}: registerNewUser(NewUserRequest)
	{GET [/v1/users/me]}: getLoggedUser()
	{PUT [/v1/users/email]}: registerNewUserByEmail(UserEmailUpdateRequest)
	{GET [/v1/users/change-reminder]}: changeReminder()
2025-08-24 20:51:51.687 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ConfigurationController:
	{PUT [/v1/admin/configurations/{uuid}]}: updateConfiguration(UUID,ConfigurationRequest)
	{POST [/v1/admin/configurations/list]}: getList(ConfigurationQueryRequest)
	{POST [/v1/admin/configurations/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{POST [/v1/admin/configurations/cate]}: getCate(ConfigurationQueryRequest)
	{PUT [/v1/admin/configurations/cate]}: updateListConf(Map)
2025-08-24 20:51:51.688 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.CustomerController:
	{POST [/v1/admin/customers/{uuid}/active]}: active(UUID)
	{POST [/v1/admin/customers/list]}: getList(CustomerQueryRequest)
	{GET [/v1/admin/customers/{uuid}]}: resetPassword(UUID)
	{POST [/v1/admin/customers/{uuid}/reset-password/{new-password}]}: resetPassword(UUID,String)
	{POST [/v1/admin/customers/{uuid}/topup/{amount}]}: topup(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/refund/{amount}]}: refund(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/suspended]}: suspended(UUID)
	{ [/v1/admin/customers/excel]}: excel(HttpServletResponse)
2025-08-24 20:51:51.690 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.LicenseController:
	{ [/v1/admin/licenses/tracking]}: excel(HttpServletResponse,UUID)
	{POST [/v1/admin/licenses/list]}: getLicenses(LicenseQueryRequest,HttpServletRequest)
	{PUT [/v1/admin/licenses/{uuid}]}: updateLicense(UUID,LicenseRequest)
	{POST [/v1/admin/licenses/update-license]}: updateLicenseStatus(UpdateLicenseStatusRequest)
	{POST [/v1/admin/licenses/switch-modem]}: switchNewModem(SwitchModemRequest)
	{POST [/v1/admin/licenses/change-proxy]}: changeAvailablePort(LicenseChangeProxyRequest)
2025-08-24 20:51:51.691 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MailTemplateController:
	{POST [/v1/admin/mail-templates/list]}: getList(MailTemplateQueryRequest)
	{POST [/v1/admin/mail-templates/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{PUT [/v1/admin/mail-templates/{uuid}]}: updateMailTemplate(UUID,MailTemplateRequest)
	{POST [/v1/admin/mail-templates]}: insertMailTemplate(MailTemplateRequest)
2025-08-24 20:51:51.692 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ModemController:
	{DELETE [/v1/admin/modems/{uuid}]}: delete(UUID)
	{GET [/v1/admin/modems/{uuid}/resume]}: resume(UUID)
	{GET [/v1/admin/modems/{uuid}/sync]}: sync(UUID)
	{GET [/v1/admin/modems/{uuid}]}: detail(UUID)
	{POST [/v1/admin/modems]}: createNewModem(ModemRequest)
	{PUT [/v1/admin/modems/{uuid}]}: updateModem(UUID,ModemRequest)
	{POST [/v1/admin/modems/list]}: getModems(ModemQueryRequest)
	{GET [/v1/admin/modems/{uuid}/pause]}: pause(UUID)
	{POST [/v1/admin/modems/generate-port]}: generatePort(GeneratePortRequest)
2025-08-24 20:51:51.693 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MonitorController:
	{POST [/v1/admin/monitors/list]}: getList(MonitorQueryRequest)
2025-08-24 20:51:51.694 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.OverviewController:
	{GET [/v1/admin/overview]}: overview()
2025-08-24 20:51:51.694 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.PromotionController:
	{POST [/v1/promotions]}: createNewPromotion(PromotionRequest)
	{PUT [/v1/promotions/{uuid}]}: updatePromotion(UUID,PromotionRequest)
	{POST [/v1/promotions/list]}: getPromotions(PromotionQueryRequest)
	{DELETE [/v1/promotions/{uuid}]}: deletePromotion(UUID)
2025-08-24 20:51:51.694 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ProxyController:
	{DELETE [/v1/admin/proxies/delete]}: deleteProxies(ProxyRequest)
	{POST [/v1/admin/proxies/list]}: getProxyWans(ProxyQueryRequest)
2025-08-24 20:51:51.695 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.TransactionController:
	{POST [/v1/admin/transactions/list]}: getList(TransactionQueryRequest)
2025-08-24 20:51:51.696 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ApiController:
	{GET [/v1/api/proxy/status]}: getStatus(String)
	{GET [/v1/api/proxy/change-ip]}: changeIp(String)
	{GET [/v1/api/proxy/change-rotation-time]}: changeRotationTime(String,Integer)
2025-08-24 20:51:51.697 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientLicenseController:
	{ [/v1/client/licenses/excel]}: excel(HttpServletResponse,String)
	{POST [/v1/client/licenses/change-rotation-time]}: changeRotationTime(ChangeRotationTimeRequest)
	{POST [/v1/client/licenses/toggle-auto-renewal]}: toggleAutoRenewal(ToggleAutoRenewalRequest)
	{POST [/v1/client/licenses/purchase-vpn]}: purchaseVpn(PurchaseVpnRequest)
	{POST [/v1/client/licenses/cancel-vpn]}: cancelVpn(CancelVpnRequest)
	{POST [/v1/client/licenses/list]}: getClientLicenses(LicenseQueryRequest)
	{POST [/v1/client/licenses/extend]}: renewIp(ExtendLicenseRequest)
	{POST [/v1/client/licenses/express-extend]}: expressExtend(ExpressExtendLicenseRequest)
	{POST [/v1/client/licenses/update-tcp-os]}: updateTcpOS(UpdateTcpOSRequest)
2025-08-24 20:51:51.697 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPackageController:
	{POST [/v1/client/packages/list]}: getPackages(PackageQueryRequest)
2025-08-24 20:51:51.697 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPromotionController:
	{GET [/v1/client/promotions/redeem-code]}: redeemCode(String,BigDecimal)
	{POST [/v1/client/promotions/quantity]}: getPromotionQuantity(PromotionQueryRequest)
	{GET [/v1/client/promotions/discount]}: getDiscountByPromotion(String,BigDecimal)
2025-08-24 20:51:51.698 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientProxyController:
	{POST [/v1/client/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{POST [/v1/client/proxies/reboot-device]}: rebootDevice(ProxyRequest)
	{POST [/v1/client/proxies/change-location]}: changeLocation(ProxyRequest)
	{POST [/v1/client/proxies/update-authenticate]}: updateAuthenticate(ProxyRequest)
	{POST [/v1/client/proxies/extend-license]}: extendLicense(ExtendLicenseRequest)
	{POST [/v1/client/proxies/change-ip]}: changeProxyIp(ProxyRequest)
2025-08-24 20:51:51.698 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionController:
	{POST [/v1/client/transactions/list]}: getList(TransactionQueryRequest)
	{POST [/v1/client/transactions/place-order]}: createNewOrder(OrderProxyRequest)
2025-08-24 20:51:51.698 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionV2Controller:
	{POST [/v1/client/orders/place-order]}: checkOrder(OrderProxyV2Request)
2025-08-24 20:51:51.698 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.PaymentController:
	{GET [/v1/client/payments/minimum-amount]}: getMinimumAmount(String)
	{POST [/v1/client/payments/topup]}: topup(TopupRequest)
	{GET [/v1/client/payments/full-currencies]}: getFullCurrencies()
	{GET [/v1/client/payments/estimated-price]}: getEstimatedPrice(String,String,BigDecimal)
	{POST [/v1/client/payments/webhook]}: webhook(String,String)
	{GET [/v1/client/payments/currencies]}: getCurrencies()
	{GET [/v1/client/payments/get-payment-url]}: getPaymentUrl(BigDecimal)
2025-08-24 20:51:51.699 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.VpnController:
	{POST [/v1/client/vpn/change]}: change(VpnRequest)
	{GET [/v1/client/vpn/download]}: downloadFile(String)
2025-08-24 20:51:51.700 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-08-24 20:51:51.706 [[main]] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-24 20:51:51.715 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-08-24 20:51:51.724 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-08-24 20:51:51.848 [[main]] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-08-24 20:51:51.871 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-24 20:51:51.880 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/neoproxy'
2025-08-24 20:51:51.891 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Started NeoProxyApiApplication in 2.695 seconds (JVM running for 3.013)
2025-08-24 20:52:20.469 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:52:20.469 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 20:52:50.470 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:52:50.470 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 20:53:20.470 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:53:20.471 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 20:53:50.471 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:53:50.472 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 20:54:20.472 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:54:20.472 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 20:54:46.779 [[http-nio-8080-exec-1]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-24 20:54:46.779 [[http-nio-8080-exec-1]] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-24 20:54:46.780 [[http-nio-8080-exec-1]] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-24 20:54:50.473 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:54:50.473 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 20:55:20.473 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:55:20.473 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 20:55:49.266 [[http-nio-8080-exec-4]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-24 - 2025-08-25T00:00
2025-08-24 20:55:49.266 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-24 20:55:49.266 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 20:55:49.267 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 20:55:50.295 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 20:55:50.295 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 20:55:50.295 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 20:55:50.297 [[http-nio-8080-exec-7]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 20:55:50.297 [[http-nio-8080-exec-7]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 20:55:50.298 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-24 20:55:50.298 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 20:55:50.298 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 20:55:50.473 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=1, idle=9, waiting=0)
2025-08-24 20:55:50.474 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 20:55:50.531 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 20:55:50.531 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 20:55:50.531 [[http-nio-8080-exec-7]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 20:55:50.531 [[http-nio-8080-exec-7]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 20:55:50.532 [[http-nio-8080-exec-7]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 20:55:58.629 [[http-nio-8080-exec-2]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-24 - 2025-08-25T00:00
2025-08-24 20:55:58.629 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-24 20:55:58.629 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 20:55:58.629 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 20:55:58.854 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 20:55:58.854 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 20:55:58.854 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 20:55:58.854 [[http-nio-8080-exec-6]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 20:55:58.855 [[http-nio-8080-exec-6]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 20:55:58.856 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-24 20:55:58.856 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 20:55:58.856 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 20:55:59.082 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 20:55:59.082 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 20:55:59.082 [[http-nio-8080-exec-6]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 20:55:59.082 [[http-nio-8080-exec-6]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 20:55:59.082 [[http-nio-8080-exec-6]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 20:56:11.547 [[SpringApplicationShutdownHook]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Closing JPA EntityManagerFactory for persistence unit 'default'
2025-08-24 20:56:11.548 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-08-24 20:56:11.548 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Before shutdown stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 20:56:11.548 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@27dbaa33: (connection evicted)
2025-08-24 20:56:11.549 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@10ef744a: (connection evicted)
2025-08-24 20:56:11.549 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@463c1c15: (connection evicted)
2025-08-24 20:56:11.549 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@7c0889d2: (connection evicted)
2025-08-24 20:56:11.550 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@5af2fef2: (connection evicted)
2025-08-24 20:56:11.550 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@2b9e5469: (connection evicted)
2025-08-24 20:56:11.550 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@66d62a86: (connection evicted)
2025-08-24 20:56:11.550 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@71734557: (connection evicted)
2025-08-24 20:56:11.551 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@624e0cce: (connection evicted)
2025-08-24 20:56:11.551 [[HikariPool-1 connection closer]] DEBUG com.zaxxer.hikari.pool.PoolBase - HikariPool-1 - Closing connection org.postgresql.jdbc.PgConnection@3b2a31f2: (connection evicted)
2025-08-24 20:56:11.551 [[SpringApplicationShutdownHook]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After shutdown stats (total=0, active=0, idle=0, waiting=0)
2025-08-24 20:56:11.551 [[SpringApplicationShutdownHook]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-08-24 21:02:01.469 [[background-preinit]] DEBUG org.jboss.logging - Logging Provider: org.jboss.logging.Log4j2LoggerProvider
2025-08-24 21:02:01.470 [[background-preinit]] INFO  o.h.validator.internal.util.Version - HV000001: Hibernate Validator 6.2.0.Final
2025-08-24 21:02:01.474 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Starting NeoProxyApiApplication using Java 17.0.16 on vinhbui-HP-EliteBook-845-14-inch-G10-Notebook-PC with PID 88713 (/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes started by vinhbui8 in /home/<USER>/Documents/aaaproxy/neo-proxy_app)
2025-08-24 21:02:01.474 [[main]] INFO  c.n.pro.NeoProxyApiApplication - The following profiles are active: dev
2025-08-24 21:02:01.898 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-08-24 21:02:01.946 [[main]] INFO  o.s.d.r.c.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 45 ms. Found 14 JPA repository interfaces.
2025-08-24 21:02:02.204 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port(s): 8080 (http)
2025-08-24 21:02:02.208 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-08-24 21:02:02.208 [[main]] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-08-24 21:02:02.208 [[main]] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/9.0.56]
2025-08-24 21:02:02.246 [[main]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring embedded WebApplicationContext
2025-08-24 21:02:02.246 [[main]] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 751 ms
2025-08-24 21:02:02.272 [[main]] DEBUG i.m.c.u.i.l.InternalLoggerFactory - Using SLF4J as the default logging framework
2025-08-24 21:02:02.353 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - Driver class org.postgresql.Driver found in Thread context class loader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09

2025-08-24 21:02:02.386 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - AWS SDK available: false
2025-08-24 21:02:02.386 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - Google Cloud Storage available: false
2025-08-24 21:02:02.387 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/callback' ...
2025-08-24 21:02:02.387 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/callback using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-24 21:02:02.387 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Unable to resolve location classpath:db/callback.
2025-08-24 21:02:02.387 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classpath resources at 'classpath:db/migration' ...
2025-08-24 21:02:02.387 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Determining location urls for classpath:db/migration using ClassLoader TomcatEmbeddedWebappClassLoader
  context: neoproxy
  delegate: true
----------> Parent Classloader:
jdk.internal.loader.ClassLoaders$AppClassLoader@30946e09
 ...
2025-08-24 21:02:02.387 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning URL: file:/home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration
2025-08-24 21:02:02.387 [[main]] DEBUG o.f.c.internal.util.FeatureDetector - JBoss VFS v2 available: false
2025-08-24 21:02:02.387 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning starting at classpath root in filesystem: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.c.FileSystemClassPathLocationScanner - Scanning for resources in path: /home/<USER>/Documents/aaaproxy/neo-proxy_app/target/classes/db/migration (db/migration)
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql
2025-08-24 21:02:02.388 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V1__CREATE_TABLE_USERS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V20__UPDATE_TABLE_USER.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V25__UPDATE_TABLE_MAIL.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V2__CREATE_TABLE_MODEMS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V32__CREATE_TABLE_PROXYS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V38__UPDATE_TABLE_MAIL.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V4__CREATE_TABLE_LICENSES.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V7__CREATE_TABLE_MONITORS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Found resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql
2025-08-24 21:02:02.389 [[main]] DEBUG o.f.c.i.s.classpath.ClassPathScanner - Scanning for classes at classpath:db/migration
2025-08-24 21:02:02.390 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - HikariPool-1 - configuration:
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - allowPoolSuspension................................false
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - autoCommit................................true
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - catalog................................none
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionInitSql................................none
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTestQuery................................none
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - connectionTimeout................................30000
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSource................................none
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceClassName................................none
2025-08-24 21:02:02.391 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceJNDI................................none
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - dataSourceProperties................................{password=<masked>}
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - driverClassName................................"org.postgresql.Driver"
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - exceptionOverrideClassName................................none
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckProperties................................{}
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - healthCheckRegistry................................none
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - idleTimeout................................***********-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - initializationFailTimeout................................1
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - isolateInternalQueries................................false
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - jdbcUrl................................********************************************
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - keepaliveTime................................0
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - leakDetectionThreshold................................0
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maxLifetime................................1800000
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - maximumPoolSize................................10
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricRegistry................................none
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - metricsTrackerFactory................................none
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - minimumIdle................................10
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - password................................<masked>
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - poolName................................"HikariPool-1"
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - readOnly................................false
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - registerMbeans................................false
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - scheduledExecutor................................none
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - schema................................none
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - threadFactory................................internal
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - transactionIsolation................................default
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - username................................"admin"
2025-08-24 21:02:02.392 [[main]] DEBUG com.zaxxer.hikari.HikariConfig - validationTimeout................................5000
2025-08-24 21:02:02.392 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-08-24 21:02:02.496 [[main]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@48cf8414
2025-08-24 21:02:02.497 [[main]] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-08-24 21:02:02.506 [[main]] INFO  o.f.c.i.license.VersionPrinter - Flyway Community Edition 8.0.5 by Redgate
2025-08-24 21:02:02.506 [[main]] INFO  o.f.c.i.d.base.BaseDatabaseType - Database: ******************************************** (PostgreSQL 16.9)
2025-08-24 21:02:02.506 [[main]] DEBUG o.f.c.i.d.base.BaseDatabaseType - Driver  : PostgreSQL JDBC Driver 42.3.1
2025-08-24 21:02:02.507 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - DDL Transactions Supported: true
2025-08-24 21:02:02.508 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Schemas: 
2025-08-24 21:02:02.508 [[main]] DEBUG o.f.c.i.s.SchemaHistoryFactory - Default schema: null
2025-08-24 21:02:02.511 [[main]] WARN  o.f.c.i.database.base.Database - Flyway upgrade recommended: PostgreSQL 16.9 is newer than this version of Flyway and support has not been tested. The latest supported version of PostgreSQL is 14.
2025-08-24 21:02:02.511 [[main]] DEBUG o.f.c.i.c.SqlScriptCallbackFactory - Scanning for SQL callbacks ...
2025-08-24 21:02:02.516 [[main]] DEBUG o.f.core.internal.command.DbValidate - Validating migrations ...
2025-08-24 21:02:02.525 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V20__UPDATE_TABLE_USER.sql (filename: V20__UPDATE_TABLE_USER.sql)
2025-08-24 21:02:02.525 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql (filename: V31__UPDATE_TABLE_LICENSE_FOR_SELLING.sql)
2025-08-24 21:02:02.525 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V11__CREATE_TABLE_TRACKINGS.sql (filename: V11__CREATE_TABLE_TRACKINGS.sql)
2025-08-24 21:02:02.525 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V2__CREATE_TABLE_MODEMS.sql (filename: V2__CREATE_TABLE_MODEMS.sql)
2025-08-24 21:02:02.525 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V38__UPDATE_TABLE_MAIL.sql (filename: V38__UPDATE_TABLE_MAIL.sql)
2025-08-24 21:02:02.525 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V25__UPDATE_TABLE_MAIL.sql (filename: V25__UPDATE_TABLE_MAIL.sql)
2025-08-24 21:02:02.525 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V6__CREATE_TABLE_TRANSACTIONS.sql (filename: V6__CREATE_TABLE_TRANSACTIONS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V18__UPDATE_TABLE_TRANSACTION.sql (filename: V18__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V12__UPDATE_TABLE_TRANSACTIONS.sql (filename: V12__UPDATE_TABLE_TRANSACTIONS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V39__UPDATE_TABLE_TRANSACTION.sql (filename: V39__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V4__CREATE_TABLE_LICENSES.sql (filename: V4__CREATE_TABLE_LICENSES.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V22__UPDATE_TABLE_CONFIG.sql (filename: V22__UPDATE_TABLE_CONFIG.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V14__UPDATE_TABLE_PROXYS.sql (filename: V14__UPDATE_TABLE_PROXYS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V8__CREATE_TABLE_REFRESH_TOKENS.sql (filename: V8__CREATE_TABLE_REFRESH_TOKENS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V23__CREATE_TABLE_MAIL_TEMPLATE.sql (filename: V23__CREATE_TABLE_MAIL_TEMPLATE.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V35__UPDATE_TABLE_PACKAGES.sql (filename: V35__UPDATE_TABLE_PACKAGES.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V15__UPDATE_TABLE_TRANSACTION.sql (filename: V15__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V17__CREATE_TABLE_PROMOTION.sql (filename: V17__CREATE_TABLE_PROMOTION.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V19__UPDATE_TABLE_LICENSES.sql (filename: V19__UPDATE_TABLE_LICENSES.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V1__CREATE_TABLE_USERS.sql (filename: V1__CREATE_TABLE_USERS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql (filename: V34__UPDATE_TABLE_PROXY_FOR_SPEED_TEST.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V16__UPDATE_TABLE_CONFIG.sql (filename: V16__UPDATE_TABLE_CONFIG.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V37__UPDATE_TABLE_LICENSES.sql (filename: V37__UPDATE_TABLE_LICENSES.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V10__CREATE_TABLE_LOCATIONS.sql (filename: V10__CREATE_TABLE_LOCATIONS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V33__UPDATE_TABLE_PROXY_FOR_ISP.sql (filename: V33__UPDATE_TABLE_PROXY_FOR_ISP.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V5__CREATE_TABLE_PACKAGES.sql (filename: V5__CREATE_TABLE_PACKAGES.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V40__UPDATE_TABLE_PROMOTION.sql (filename: V40__UPDATE_TABLE_PROMOTION.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V32__CREATE_TABLE_PROXYS.sql (filename: V32__CREATE_TABLE_PROXYS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql (filename: V42__UPDATE_TABLE_PACKAGES_ADD_LOCATION_FEE.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V9__CREATE_TABLE_CONFIGURATIONS.sql (filename: V9__CREATE_TABLE_CONFIGURATIONS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V7__CREATE_TABLE_MONITORS.sql (filename: V7__CREATE_TABLE_MONITORS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql (filename: V41__UPDATE_TABLE_LICENSES_AUTO_RENEWAL.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V21__UPDATE_TABLE_TRANSACTION.sql (filename: V21__UPDATE_TABLE_TRANSACTION.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V24__UPDATE_TABLE_PROXYS.sql (filename: V24__UPDATE_TABLE_PROXYS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V13__UPDATE_TABLE_PROXYS.sql (filename: V13__UPDATE_TABLE_PROXYS.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V36__UPDATE_TABLE_LICENSES.sql (filename: V36__UPDATE_TABLE_LICENSES.sql)
2025-08-24 21:02:02.526 [[main]] DEBUG o.f.core.internal.scanner.Scanner - Filtering out resource: db/migration/V3__CREATE_TABLE_PROXY_WANS.sql (filename: V3__CREATE_TABLE_PROXY_WANS.sql)
2025-08-24 21:02:02.535 [[main]] INFO  o.f.core.internal.command.DbValidate - Successfully validated 37 migrations (execution time 00:00.018s)
2025-08-24 21:02:02.539 [[main]] INFO  o.f.core.internal.command.DbMigrate - Current version of schema "public": 42
2025-08-24 21:02:02.539 [[main]] INFO  o.f.core.internal.command.DbMigrate - Schema "public" is up to date. No migration necessary.
2025-08-24 21:02:02.542 [[main]] DEBUG org.flywaydb.core.FlywayExecutor - Memory usage: 63 of 136M
2025-08-24 21:02:02.576 [[main]] INFO  o.h.jpa.internal.util.LogHelper - HHH000204: Processing PersistenceUnitInfo [name: default]
2025-08-24 21:02:02.594 [[main]] INFO  org.hibernate.Version - HHH000412: Hibernate ORM core version 5.6.3.Final
2025-08-24 21:02:02.597 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=1, active=0, idle=1, waiting=0)
2025-08-24 21:02:02.616 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@5411b95c
2025-08-24 21:02:02.634 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@494bf42c
2025-08-24 21:02:02.652 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@4154f409
2025-08-24 21:02:02.653 [[main]] INFO  o.h.annotations.common.Version - HCANN000001: Hibernate Commons Annotations {5.1.2.Final}
2025-08-24 21:02:02.669 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@57a8829b
2025-08-24 21:02:02.686 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@7a8a524f
2025-08-24 21:02:02.696 [[main]] INFO  org.hibernate.dialect.Dialect - HHH000400: Using dialect: org.hibernate.dialect.PostgreSQLDialect
2025-08-24 21:02:02.702 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6925bbdf
2025-08-24 21:02:02.719 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@33907584
2025-08-24 21:02:02.735 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@6f1fe1de
2025-08-24 21:02:02.756 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection org.postgresql.jdbc.PgConnection@472fde58
2025-08-24 21:02:02.756 [[HikariPool-1 connection adder]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - After adding stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:02:03.089 [[main]] INFO  o.h.e.t.j.p.i.JtaPlatformInitiator - HHH000490: Using JtaPlatform implementation: [org.hibernate.engine.transaction.jta.platform.internal.NoJtaPlatform]
2025-08-24 21:02:03.093 [[main]] INFO  o.s.o.j.LocalContainerEntityManagerFactoryBean - Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-08-24 21:02:03.615 [[main]] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-08-24 21:02:03.793 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/v1/rh-websocket/**'] with []
2025-08-24 21:02:03.793 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/index.html'] with []
2025-08-24 21:02:03.793 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/app.js'] with []
2025-08-24 21:02:03.793 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure Ant [pattern='/favicon.ico'] with []
2025-08-24 21:02:03.810 [[main]] INFO  o.s.s.web.DefaultSecurityFilterChain - Will secure any request with [org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter@7053b64b, org.springframework.security.web.context.SecurityContextPersistenceFilter@561b4c61, org.springframework.security.web.header.HeaderWriterFilter@6e3552b0, org.springframework.security.web.authentication.logout.LogoutFilter@489a722d, com.neoproxy.pro.config.JwtRequestFilter@59f36439, org.springframework.security.web.savedrequest.RequestCacheAwareFilter@63fef83c, org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter@2c69ea30, org.springframework.security.web.authentication.AnonymousAuthenticationFilter@7f5b0926, org.springframework.security.web.session.SessionManagementFilter@17c3e33, org.springframework.security.web.access.ExceptionTranslationFilter@4faccb34, org.springframework.security.web.access.intercept.FilterSecurityInterceptor@8c54f48]
2025-08-24 21:02:03.904 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.AuthenticationController:
	{POST [/v1/users/authentication/email]}: authenticateByEmail(EmailAuthenticationRequest)
2025-08-24 21:02:03.906 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.ISPController:
	{GET [/v1/isp/list]}: getLocations()
2025-08-24 21:02:03.907 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.LocationController:
	{POST [/v1/locations/list]}: getLocations()
	{GET [/v1/locations/list]}: getList()
	{GET [/v1/locations/full-list]}: getFullList()
2025-08-24 21:02:03.907 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PackageController:
	{POST [/v1/packages/list]}: getPackages(PackageQueryRequest)
	{POST [/v1/packages]}: createNewPackage(PackageRequest)
	{PUT [/v1/packages/{uuid}]}: updatePackage(UUID,PackageRequest)
	{DELETE [/v1/packages/{uuid}]}: deletePackage(UUID)
2025-08-24 21:02:03.908 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.PingController:
	{GET [/v1/ping]}: ping()
2025-08-24 21:02:03.909 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.UserController:
	{PUT [/v1/users/reset-password]}: resetPassword(ResetPasswordRequest)
	{POST [/v1/users/change-password]}: changePassword(ResetPasswordRequest)
	{GET [/v1/users/verify/{code}]}: verifyAccount(String)
	{POST [/v1/users/register]}: registerNewUser(NewUserRequest)
	{GET [/v1/users/me]}: getLoggedUser()
	{PUT [/v1/users/email]}: registerNewUserByEmail(UserEmailUpdateRequest)
	{GET [/v1/users/change-reminder]}: changeReminder()
2025-08-24 21:02:03.910 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ConfigurationController:
	{PUT [/v1/admin/configurations/{uuid}]}: updateConfiguration(UUID,ConfigurationRequest)
	{POST [/v1/admin/configurations/list]}: getList(ConfigurationQueryRequest)
	{POST [/v1/admin/configurations/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{POST [/v1/admin/configurations/cate]}: getCate(ConfigurationQueryRequest)
	{PUT [/v1/admin/configurations/cate]}: updateListConf(Map)
2025-08-24 21:02:03.911 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.CustomerController:
	{POST [/v1/admin/customers/{uuid}/active]}: active(UUID)
	{POST [/v1/admin/customers/list]}: getList(CustomerQueryRequest)
	{GET [/v1/admin/customers/{uuid}]}: resetPassword(UUID)
	{POST [/v1/admin/customers/{uuid}/reset-password/{new-password}]}: resetPassword(UUID,String)
	{POST [/v1/admin/customers/{uuid}/topup/{amount}]}: topup(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/refund/{amount}]}: refund(UUID,BigDecimal)
	{POST [/v1/admin/customers/{uuid}/suspended]}: suspended(UUID)
	{ [/v1/admin/customers/excel]}: excel(HttpServletResponse)
2025-08-24 21:02:03.913 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.LicenseController:
	{ [/v1/admin/licenses/tracking]}: excel(HttpServletResponse,UUID)
	{POST [/v1/admin/licenses/list]}: getLicenses(LicenseQueryRequest,HttpServletRequest)
	{PUT [/v1/admin/licenses/{uuid}]}: updateLicense(UUID,LicenseRequest)
	{POST [/v1/admin/licenses/update-license]}: updateLicenseStatus(UpdateLicenseStatusRequest)
	{POST [/v1/admin/licenses/switch-modem]}: switchNewModem(SwitchModemRequest)
	{POST [/v1/admin/licenses/change-proxy]}: changeAvailablePort(LicenseChangeProxyRequest)
2025-08-24 21:02:03.914 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MailTemplateController:
	{POST [/v1/admin/mail-templates/list]}: getList(MailTemplateQueryRequest)
	{POST [/v1/admin/mail-templates/send-notify-email]}: sendNotifyEmail(EmailNotifyRequest)
	{PUT [/v1/admin/mail-templates/{uuid}]}: updateMailTemplate(UUID,MailTemplateRequest)
	{POST [/v1/admin/mail-templates]}: insertMailTemplate(MailTemplateRequest)
2025-08-24 21:02:03.916 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ModemController:
	{DELETE [/v1/admin/modems/{uuid}]}: delete(UUID)
	{GET [/v1/admin/modems/{uuid}/resume]}: resume(UUID)
	{GET [/v1/admin/modems/{uuid}/sync]}: sync(UUID)
	{GET [/v1/admin/modems/{uuid}]}: detail(UUID)
	{POST [/v1/admin/modems]}: createNewModem(ModemRequest)
	{PUT [/v1/admin/modems/{uuid}]}: updateModem(UUID,ModemRequest)
	{POST [/v1/admin/modems/list]}: getModems(ModemQueryRequest)
	{GET [/v1/admin/modems/{uuid}/pause]}: pause(UUID)
	{POST [/v1/admin/modems/generate-port]}: generatePort(GeneratePortRequest)
2025-08-24 21:02:03.916 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.MonitorController:
	{POST [/v1/admin/monitors/list]}: getList(MonitorQueryRequest)
2025-08-24 21:02:03.916 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.OverviewController:
	{GET [/v1/admin/overview]}: overview()
2025-08-24 21:02:03.916 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.PromotionController:
	{POST [/v1/promotions]}: createNewPromotion(PromotionRequest)
	{PUT [/v1/promotions/{uuid}]}: updatePromotion(UUID,PromotionRequest)
	{POST [/v1/promotions/list]}: getPromotions(PromotionQueryRequest)
	{DELETE [/v1/promotions/{uuid}]}: deletePromotion(UUID)
2025-08-24 21:02:03.917 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ProxyController:
	{DELETE [/v1/admin/proxies/delete]}: deleteProxies(ProxyRequest)
	{POST [/v1/admin/proxies/list]}: getProxyWans(ProxyQueryRequest)
2025-08-24 21:02:03.917 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.TransactionController:
	{POST [/v1/admin/transactions/list]}: getList(TransactionQueryRequest)
2025-08-24 21:02:03.918 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.a.ApiController:
	{GET [/v1/api/proxy/status]}: getStatus(String)
	{GET [/v1/api/proxy/change-ip]}: changeIp(String)
	{GET [/v1/api/proxy/change-rotation-time]}: changeRotationTime(String,Integer)
2025-08-24 21:02:03.918 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientLicenseController:
	{ [/v1/client/licenses/excel]}: excel(HttpServletResponse,String)
	{POST [/v1/client/licenses/change-rotation-time]}: changeRotationTime(ChangeRotationTimeRequest)
	{POST [/v1/client/licenses/toggle-auto-renewal]}: toggleAutoRenewal(ToggleAutoRenewalRequest)
	{POST [/v1/client/licenses/purchase-vpn]}: purchaseVpn(PurchaseVpnRequest)
	{POST [/v1/client/licenses/cancel-vpn]}: cancelVpn(CancelVpnRequest)
	{POST [/v1/client/licenses/list]}: getClientLicenses(LicenseQueryRequest)
	{POST [/v1/client/licenses/extend]}: renewIp(ExtendLicenseRequest)
	{POST [/v1/client/licenses/express-extend]}: expressExtend(ExpressExtendLicenseRequest)
	{POST [/v1/client/licenses/update-tcp-os]}: updateTcpOS(UpdateTcpOSRequest)
2025-08-24 21:02:03.919 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPackageController:
	{POST [/v1/client/packages/list]}: getPackages(PackageQueryRequest)
2025-08-24 21:02:03.919 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientPromotionController:
	{GET [/v1/client/promotions/redeem-code]}: redeemCode(String,BigDecimal)
	{POST [/v1/client/promotions/quantity]}: getPromotionQuantity(PromotionQueryRequest)
	{GET [/v1/client/promotions/discount]}: getDiscountByPromotion(String,BigDecimal)
2025-08-24 21:02:03.920 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientProxyController:
	{POST [/v1/client/proxies/list]}: getProxyWans(ProxyQueryRequest)
	{POST [/v1/client/proxies/reboot-device]}: rebootDevice(ProxyRequest)
	{POST [/v1/client/proxies/change-location]}: changeLocation(ProxyRequest)
	{POST [/v1/client/proxies/update-authenticate]}: updateAuthenticate(ProxyRequest)
	{POST [/v1/client/proxies/extend-license]}: extendLicense(ExtendLicenseRequest)
	{POST [/v1/client/proxies/change-ip]}: changeProxyIp(ProxyRequest)
2025-08-24 21:02:03.921 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionController:
	{POST [/v1/client/transactions/list]}: getList(TransactionQueryRequest)
	{POST [/v1/client/transactions/place-order]}: createNewOrder(OrderProxyRequest)
2025-08-24 21:02:03.921 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.ClientTransactionV2Controller:
	{POST [/v1/client/orders/place-order]}: checkOrder(OrderProxyV2Request)
2025-08-24 21:02:03.922 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.PaymentController:
	{GET [/v1/client/payments/minimum-amount]}: getMinimumAmount(String)
	{POST [/v1/client/payments/topup]}: topup(TopupRequest)
	{GET [/v1/client/payments/full-currencies]}: getFullCurrencies()
	{GET [/v1/client/payments/estimated-price]}: getEstimatedPrice(String,String,BigDecimal)
	{POST [/v1/client/payments/webhook]}: webhook(String,String)
	{GET [/v1/client/payments/currencies]}: getCurrencies()
	{GET [/v1/client/payments/get-payment-url]}: getPaymentUrl(BigDecimal)
2025-08-24 21:02:03.922 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	c.n.p.c.c.VpnController:
	{POST [/v1/client/vpn/change]}: change(VpnRequest)
	{GET [/v1/client/vpn/download]}: downloadFile(String)
2025-08-24 21:02:03.923 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 
	o.s.b.a.w.s.e.BasicErrorController:
	{ [/error]}: error(HttpServletRequest)
	{ [/error], produces [text/html]}: errorHtml(HttpServletRequest,HttpServletResponse)
2025-08-24 21:02:03.929 [[main]] INFO  o.s.b.a.w.s.WelcomePageHandlerMapping - Adding welcome page: class path resource [static/index.html]
2025-08-24 21:02:03.938 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'beanNameHandlerMapping' {}
2025-08-24 21:02:03.948 [[main]] DEBUG _.s.w.s.HandlerMapping.Mappings - 'resourceHandlerMapping' {/webjars/**=ResourceHttpRequestHandler [classpath [META-INF/resources/webjars/]], /**=ResourceHttpRequestHandler [classpath [META-INF/resources/], classpath [resources/], classpath [static/], classpath [public/], ServletContext [/]]}
2025-08-24 21:02:04.094 [[main]] INFO  o.s.b.a.e.web.EndpointLinksResolver - Exposing 6 endpoint(s) beneath base path '/actuator'
2025-08-24 21:02:04.117 [[main]] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-08-24 21:02:04.123 [[main]] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat started on port(s): 8080 (http) with context path '/neoproxy'
2025-08-24 21:02:04.133 [[main]] INFO  c.n.pro.NeoProxyApiApplication - Started NeoProxyApiApplication in 2.827 seconds (JVM running for 3.136)
2025-08-24 21:02:32.598 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:02:32.598 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:03:02.598 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:03:02.598 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:03:23.603 [[http-nio-8080-exec-5]] INFO  o.a.c.c.C.[.[localhost].[/neoproxy] - Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-24 21:03:23.603 [[http-nio-8080-exec-5]] INFO  o.s.web.servlet.DispatcherServlet - Initializing Servlet 'dispatcherServlet'
2025-08-24 21:03:23.604 [[http-nio-8080-exec-5]] INFO  o.s.web.servlet.DispatcherServlet - Completed initialization in 1 ms
2025-08-24 21:03:23.785 [[http-nio-8080-exec-1]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-24 - 2025-08-25T00:00
2025-08-24 21:03:23.788 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-24 21:03:23.789 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 21:03:23.789 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 21:03:24.853 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 21:03:24.853 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 21:03:24.853 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 21:03:24.856 [[http-nio-8080-exec-5]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 21:03:24.856 [[http-nio-8080-exec-5]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 21:03:24.857 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-24 21:03:24.857 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 21:03:24.857 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 21:03:25.087 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 21:03:25.087 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 21:03:25.087 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 21:03:25.088 [[http-nio-8080-exec-5]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 21:03:25.088 [[http-nio-8080-exec-5]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 21:03:28.786 [[http-nio-8080-exec-6]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-24 - 2025-08-25T00:00
2025-08-24 21:03:28.786 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-24 21:03:28.786 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 21:03:28.786 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 21:03:29.018 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 21:03:29.018 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 21:03:29.018 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 21:03:29.018 [[http-nio-8080-exec-8]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 21:03:29.019 [[http-nio-8080-exec-8]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 21:03:29.020 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-24 21:03:29.021 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 21:03:29.021 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 21:03:29.252 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 21:03:29.252 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 21:03:29.252 [[http-nio-8080-exec-8]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 21:03:29.252 [[http-nio-8080-exec-8]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 21:03:29.253 [[http-nio-8080-exec-8]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 21:03:32.599 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:03:32.599 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:04:02.599 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:04:02.599 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:04:10.391 [[http-nio-8080-exec-1]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-24 - 2025-08-25T00:00
2025-08-24 21:04:10.391 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/merchant/coins]
2025-08-24 21:04:10.391 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 21:04:10.391 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 21:04:10.648 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 21:04:10.648 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 21:04:10.648 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 21:04:10.649 [[http-nio-8080-exec-5]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/merchant/coins
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getSelectedCurrencies(NowPaymentServiceImpl.java:58)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:72)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 21:04:10.649 [[http-nio-8080-exec-5]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 21:04:10.650 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with url [https://api-sandbox.nowpayments.io/v1/full-currencies]
2025-08-24 21:04:10.650 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [Content-Type: application/json]
2025-08-24 21:04:10.650 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Send partner with header [x-api-key: DEFAULT]
2025-08-24 21:04:10.902 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - PARTNER RESPONSE, HTTP CODE: [403]
2025-08-24 21:04:10.902 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Error message: [{"status":false,"statusCode":403,"code":"INVALID_API_KEY","message":"Invalid api key"}]
2025-08-24 21:04:10.902 [[http-nio-8080-exec-5]] INFO  c.n.pro.utils.httpClient.ApiClient - Status NOT OK. throw Error code: [400]
2025-08-24 21:04:10.902 [[http-nio-8080-exec-5]] ERROR c.n.pro.utils.httpClient.ApiClient -  CALL PARTNER [Error] https://api-sandbox.nowpayments.io/v1/full-currencies
java.lang.Exception: 400 Error when map code: 403
	at com.neoproxy.pro.utils.httpClient.ApiClient.invoke(ApiClient.java:91)
	at com.neoproxy.pro.utils.httpClient.ApiClient.callAPI(ApiClient.java:61)
	at com.neoproxy.pro.nowpayments.service.impl.NowPaymentServiceImpl.getFullCurrencies(NowPaymentServiceImpl.java:73)
	at com.neoproxy.pro.controller.client.PaymentController.getFullCurrencies(PaymentController.java:46)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1067)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:963)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006)
	at org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:898)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:655)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:764)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:227)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:111)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:327)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.invoke(FilterSecurityInterceptor.java:115)
	at org.springframework.security.web.access.intercept.FilterSecurityInterceptor.doFilter(FilterSecurityInterceptor.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:122)
	at org.springframework.security.web.access.ExceptionTranslationFilter.doFilter(ExceptionTranslationFilter.java:116)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:126)
	at org.springframework.security.web.session.SessionManagementFilter.doFilter(SessionManagementFilter.java:81)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.AnonymousAuthenticationFilter.doFilter(AnonymousAuthenticationFilter.java:109)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.servletapi.SecurityContextHolderAwareRequestFilter.doFilter(SecurityContextHolderAwareRequestFilter.java:149)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.savedrequest.RequestCacheAwareFilter.doFilter(RequestCacheAwareFilter.java:63)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at com.neoproxy.pro.config.JwtRequestFilter.doFilterInternal(JwtRequestFilter.java:33)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:103)
	at org.springframework.security.web.authentication.logout.LogoutFilter.doFilter(LogoutFilter.java:89)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.header.HeaderWriterFilter.doHeadersAfter(HeaderWriterFilter.java:90)
	at org.springframework.security.web.header.HeaderWriterFilter.doFilterInternal(HeaderWriterFilter.java:75)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:110)
	at org.springframework.security.web.context.SecurityContextPersistenceFilter.doFilter(SecurityContextPersistenceFilter.java:80)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.context.request.async.WebAsyncManagerIntegrationFilter.doFilterInternal(WebAsyncManagerIntegrationFilter.java:55)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.springframework.security.web.FilterChainProxy$VirtualFilterChain.doFilter(FilterChainProxy.java:336)
	at org.springframework.security.web.FilterChainProxy.doFilterInternal(FilterChainProxy.java:211)
	at org.springframework.security.web.FilterChainProxy.doFilter(FilterChainProxy.java:183)
	at org.springframework.web.filter.DelegatingFilterProxy.invokeDelegate(DelegatingFilterProxy.java:354)
	at org.springframework.web.filter.DelegatingFilterProxy.doFilter(DelegatingFilterProxy.java:267)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.boot.actuate.metrics.web.servlet.WebMvcMetricsFilter.doFilterInternal(WebMvcMetricsFilter.java:96)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:189)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:162)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:197)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:97)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:540)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:135)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:78)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:357)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:382)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:65)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:895)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1732)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:840)
2025-08-24 21:04:10.903 [[http-nio-8080-exec-5]] ERROR c.n.p.n.s.impl.NowPaymentServiceImpl - [NowPayment] Error: 400
2025-08-24 21:04:32.599 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:04:32.600 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:05:02.600 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:05:02.600 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:05:10.207 [[http-nio-8080-exec-10]] INFO  c.n.p.s.impl.LicenseServiceImpl - 2025-08-24 - 2025-08-25T00:00
2025-08-24 21:05:32.600 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:05:32.600 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:06:02.601 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:06:02.601 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:06:32.601 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:06:32.601 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:07:02.602 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:07:02.602 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:07:32.602 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:07:32.602 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:08:02.602 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:08:02.602 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:08:32.603 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:08:32.603 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:09:02.603 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:09:02.603 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:09:32.604 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:09:32.604 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:10:02.604 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:10:02.604 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:10:32.605 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:10:32.605 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:11:02.605 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:11:02.605 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:11:32.605 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:11:32.605 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:12:02.606 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:12:02.606 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:12:32.607 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:12:32.607 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
2025-08-24 21:13:02.607 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Pool stats (total=10, active=0, idle=10, waiting=0)
2025-08-24 21:13:02.607 [[HikariPool-1 housekeeper]] DEBUG com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Fill pool skipped, pool is at sufficient level.
