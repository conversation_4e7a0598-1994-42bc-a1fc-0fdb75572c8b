package com.neoproxy.pro.service.impl;

import com.neoproxy.pro.domain.Promotion;
import com.neoproxy.pro.dto.*;
import com.neoproxy.pro.enums.ErrorCode;
import com.neoproxy.pro.enums.PromotionConditionType;
import com.neoproxy.pro.enums.PromotionDiscountType;
import com.neoproxy.pro.enums.PromotionStatus;
import com.neoproxy.pro.mapper.PromotionMapper;
import com.neoproxy.pro.mapper.PromotionMapperImpl;
import com.neoproxy.pro.repository.PromotionRepository;
import com.neoproxy.pro.service.PromotionService;
import com.neoproxy.pro.service.exception.NeoProxyServiceException;
import com.neoproxy.pro.utils.CommonUtil;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.convert.QueryByExamplePredicateBuilder;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class PromotionServiceImpl implements PromotionService {
    PromotionRepository promotionRepository;
    PromotionMapper promotionMapper = new PromotionMapperImpl();

    public Specification<Promotion> getSpecAndExample(PromotionQueryRequest request, Example<Promotion> example) {
        return (Specification<Promotion>) (root, query, builder) -> {
            final List<Predicate> predicates = new ArrayList<>();
            if (!request.getCode().isEmpty()) {
                predicates.add(builder.like(root.get("code"), "%" + request.getCode() + "%"));
            }
            predicates.add(QueryByExamplePredicateBuilder.getPredicate(root, builder, example));
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    @Override
    public TableData<PromotionDto> getPromotions(PromotionQueryRequest request) {
        TableData<PromotionDto> data = new TableData<>();
        Sort sortBy = Sort.by(Sort.Direction.DESC, "updatedAt");
        Pageable paging = PageRequest.of(request.getPage(), request.getPageSize(), sortBy);

        ExampleMatcher matcher = ExampleMatcher
                .matching()
                .withMatcher("name", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        Promotion searchPromotion = new Promotion();
        if (!CommonUtil.isEmpty(request.getStatus())) {
            searchPromotion.setStatus(PromotionStatus.valueOf(request.getStatus()));
        }
        if (!CommonUtil.isEmpty(request.getConditionType())) {
            searchPromotion.setConditionType(PromotionConditionType.valueOf(request.getConditionType()));
        }

        Example<Promotion> example = Example.of(searchPromotion, matcher);
        Page<Promotion> item = promotionRepository.findAll(getSpecAndExample(request, example), paging);

        item.getContent().forEach(salePromotion -> {
            data.getData().add(promotionMapper.toDto(salePromotion));
        });
        data.setPages(item.getTotalPages());

        return data;
    }

    @Override
    public PromotionDto createNewPromotion(@NonNull PromotionRequest promotionRequest) {
        Promotion salePromotion = Promotion.builder()
                .code(promotionRequest.getCode())
                .description(promotionRequest.getDescription())
                .discountType(promotionRequest.getDiscountType())
                .discountValue(promotionRequest.getDiscountValue())
                .minAmount(promotionRequest.getMinAmount())
                .maxDiscount(promotionRequest.getMaxDiscount())
                .startTime(promotionRequest.getStartTime())
                .endTime(promotionRequest.getEndTime())
                .status(promotionRequest.getStatus())
                .conditionType(promotionRequest.getConditionType())
                .thresholdValue(promotionRequest.getThresholdValue())
                .build();
        promotionRepository.save(salePromotion);

        return promotionMapper.toDto(salePromotion);
    }

    @Override
    public PromotionDto updatePromotion(@NonNull UUID uuid, @NonNull PromotionRequest promotionRequest) {
        Promotion promotion = promotionRepository.findByUuid(uuid);
        if (promotion == null)
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("Could not find salePromotion with this uuid")
                            .build());

        promotion.setCode(promotionRequest.getCode());
        promotion.setDescription(promotionRequest.getDescription());
        promotion.setDiscountValue(promotionRequest.getDiscountValue());
        promotion.setDiscountType(promotionRequest.getDiscountType());
        promotion.setMinAmount(promotionRequest.getMinAmount());
        promotion.setMaxDiscount(promotionRequest.getMaxDiscount());
        promotion.setStartTime(promotionRequest.getStartTime());
        promotion.setEndTime(promotionRequest.getEndTime());
        promotion.setStatus(promotionRequest.getStatus());
        promotion.setConditionType(promotionRequest.getConditionType());
        promotion.setThresholdValue(promotionRequest.getThresholdValue());

        promotionRepository.save(promotion);

        return promotionMapper.toDto(promotion);
    }

    @Override
    public boolean deletePromotion(@NonNull UUID uuid) {
        Promotion salePromotion = promotionRepository.findByUuid(uuid);
        if (salePromotion == null)
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("could not find salePromotion with this uuid")
                            .build());
        promotionRepository.delete(salePromotion);

        return true;
    }

    @Override
    public BigDecimal redeemCode(String promotionCode, BigDecimal totalAmount) {
        log.info("----- Calculate discount for promotion {}", promotionCode);
        Optional<Promotion> optional = promotionRepository.findByCode(promotionCode);
        if (optional.isEmpty()) {
            log.info("----- No promotion code found. Return 0 discount");
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("Invalid promo code.")
                            .build());
        }
        Promotion promotion = optional.get();
        if (promotion.getStatus().equals(PromotionStatus.INACTIVE)) {
            log.info("----- The promotion was not active");
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("Invalid promo code.")
                            .build());
        }
        if (LocalDateTime.now().isBefore(promotion.getStartTime())) {
            log.info("----- The promotion not start time");
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("Invalid promo code usage time.")
                            .build());
        }
        if (LocalDateTime.now().isAfter(promotion.getEndTime())) {
            log.info("----- The promotion is end time");
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("Invalid promo code usage time.")
                            .build());
        }
        if (promotion.getMinAmount().compareTo(totalAmount) > 0) {
            log.info("----- Invalid min amount");
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.BAD_DATA)
                            .message("The minimum amount is not valid with this promo code.")
                            .build());
        }

        // CALCULATOR DISCOUNT
        BigDecimal discountAmount = new BigDecimal(0);
        if (promotion.getDiscountType().equals(PromotionDiscountType.DIRECTLY)) {
            discountAmount = promotion.getDiscountValue();
        } else if (promotion.getDiscountType().equals(PromotionDiscountType.PERCENT)) {
            discountAmount = totalAmount.multiply(promotion.getDiscountValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        }

        if (discountAmount.compareTo(promotion.getMaxDiscount()) > 0) {
            discountAmount = promotion.getMaxDiscount();
            log.info("----- Discount amount after check max {}", discountAmount);
        } else {
            log.info("----- Discount amount {}", discountAmount);
        }

        return discountAmount;
    }

    @Override
    public BigDecimal getDiscountByPromotion(String uuid, BigDecimal totalAmount) {
        Promotion promotion = promotionRepository.findByUuid(UUID.fromString(uuid));
        if (promotion == null) {
            return BigDecimal.ZERO;
        }

        // CALCULATOR DISCOUNT
        BigDecimal discountAmount = new BigDecimal(0);
        if (promotion.getDiscountType().equals(PromotionDiscountType.DIRECTLY)) {
            discountAmount = promotion.getDiscountValue();
        } else if (promotion.getDiscountType().equals(PromotionDiscountType.PERCENT)) {
            discountAmount = totalAmount.multiply(promotion.getDiscountValue()).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
        }

        if (discountAmount.compareTo(promotion.getMaxDiscount()) > 0) {
            discountAmount = promotion.getMaxDiscount();
            log.info("----- Discount amount after check max {}", discountAmount);
        } else {
            log.info("----- Discount amount {}", discountAmount);
        }

        return discountAmount.setScale(2, RoundingMode.HALF_UP);
    }

    @Override
    public List<PromotionQuantityDto> getPromotionQuantity(PromotionQueryRequest request) {
        List<Promotion> promotions = promotionRepository.getPromotions(PromotionStatus.ACTIVE, PromotionConditionType.QUANTITY);

        List<PromotionQuantityDto> list = new ArrayList<>();
        for (int i = 1; i <= 20; i++) {
            Integer promoNumber = i;
            String promotionId = null;
            String promoName = "1 proxy";
            BigDecimal percent = BigDecimal.ZERO;

            if (i > 1) {
                promoName = i + " proxies";
            }

            if (!promotions.isEmpty()) {
                // Find promotions by condition
                Promotion promotion = promotions.stream().filter(el -> el.getThresholdValue().equals(promoNumber)).findFirst().orElse(null);
                if (promotion != null) {
                    promotionId = promotion.getUuid().toString();
                    promoName += " - Discount " + promotion.getDiscountValue() + "%";
                    percent = promotion.getDiscountValue();
                }
            }

            list.add(PromotionQuantityDto.builder()
                    .id(promoNumber)
                    .name(promoName)
                    .promotionId(promotionId)
                    .percent(percent)
                    .build());
        }

        return list;
    }
}
