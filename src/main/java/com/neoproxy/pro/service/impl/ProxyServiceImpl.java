package com.neoproxy.pro.service.impl;

import com.neoproxy.pro.domain.Package;
import com.neoproxy.pro.domain.*;
import com.neoproxy.pro.dto.*;
import com.neoproxy.pro.enums.*;
import com.neoproxy.pro.mapper.ProxyMapper;
import com.neoproxy.pro.mapper.TransactionMapper;
import com.neoproxy.pro.repository.LicenseRepository;
import com.neoproxy.pro.repository.ModemRepository;
import com.neoproxy.pro.repository.ProxyRepository;
import com.neoproxy.pro.repository.TransactionRepository;
import com.neoproxy.pro.repository.UserRepository;
import com.neoproxy.pro.service.AuthenticationService;
import com.neoproxy.pro.service.ProxyCoreService;
import com.neoproxy.pro.service.ProxyService;
import com.neoproxy.pro.service.exception.NeoProxyServiceException;
import com.neoproxy.pro.utils.CommonUtil;
import com.neoproxy.pro.utils.DateUtil;
import com.neoproxy.pro.xproxy.model.PortType;
import com.neoproxy.pro.xproxy.model.ProxyInfoReq;
import com.neoproxy.pro.xproxy.model.ResetDataCounterReq;
import com.neoproxy.pro.xproxy.model.XProxyWanInfo;
import com.neoproxy.pro.xproxy.service.XProxyService;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.data.jpa.convert.QueryByExamplePredicateBuilder;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.criteria.Predicate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.concurrent.atomic.AtomicInteger;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class ProxyServiceImpl implements ProxyService {
    ProxyMapper proxyMapper;
    ProxyRepository proxyRepository;
    AuthenticationService authenticationService;
    LicenseRepository licenseRepository;
    XProxyService xProxyService;
    Random rand = new Random();
    ProxyCoreService proxyCoreService;
    ModemRepository modemRepository;
    TransactionRepository transactionRepository;
    UserRepository userRepository;
    TransactionMapper transactionMapper;


    public Specification<Proxy> getSpecAndExample(ProxyQueryRequest request, Example<Proxy> example) {
        return (Specification<Proxy>) (root, query, builder) -> {
            final List<Predicate> predicates = new ArrayList<>();
            if (!request.getExpiredDate().isEmpty()) {
                predicates.add(builder.greaterThan(root.get("expiredDate"), DateUtil.convertIsoStringToDate(request.getExpiredDate())));
            }
            if (!request.getLicense().isEmpty()) {
                predicates.add(builder.like(root.get("license").get("uuid").as(String.class), "%" + request.getLicense() + "%"));
            }
            predicates.add(QueryByExamplePredicateBuilder.getPredicate(root, builder, example));
            return builder.and(predicates.toArray(new Predicate[predicates.size()]));
        };
    }

    ;

    @Override
    public TableData<ProxyDto> getProxies(ProxyQueryRequest request) {
        User user = authenticationService.getLoggedUser();
        Proxy searchProxy = new Proxy();
        if (user.isClient()) {
            License license = new License();
            license.setCustomer(user);
            searchProxy.setLicense(license);
        }

        TableData<ProxyDto> tableData = new TableData<>();
        Sort sortBy = Sort.by(Sort.Direction.ASC, "portType")
                .and(Sort.by(Sort.Direction.DESC, "saleStatus", "updatedAt"));
        Pageable paging = PageRequest.of(request.getPage(), request.getPageSize(), sortBy);
        ExampleMatcher matcher = ExampleMatcher.matching()
                .withMatcher("name", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("host", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("authenticationUsers", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase())
                .withMatcher("authorizationIps", ExampleMatcher.GenericPropertyMatchers.contains().ignoreCase());
        if (!request.getName().isEmpty()) {
            searchProxy.setModem(Modem.builder().name(request.getName()).build());
        }
        if (!request.getStatus().isEmpty()) {
            searchProxy.setStatus(ProxyStatus.findByName(request.getStatus()));
        }
        if (!request.getSaleStatus().isEmpty()) {
            searchProxy.setSaleStatus(ProxySaleStatus.valueOf(request.getSaleStatus()));
        }
        if (!request.getHost().isEmpty()) {
            searchProxy.setHost(request.getHost());
        }
        if (request.getPort() != -1) {
            searchProxy.setSharedPort(request.getPort());
        }
        if (!request.getAuthUsername().isEmpty()) {
            searchProxy.setAuthenticationUsers(request.getAuthUsername());
        }
        if (!request.getAuthIps().isEmpty()) {
            searchProxy.setAuthorizationIps(request.getAuthIps());
        }
        if (!request.getModemId().isEmpty()) {
            searchProxy.setModem(Modem.builder().uuid(UUID.fromString(request.getModemId())).build());
        }

        Example<Proxy> example = Example.of(searchProxy, matcher);
        Page<Proxy> page = proxyRepository.findAll(getSpecAndExample(request, example), paging);

        page.getContent().forEach(proxyWan -> {
            tableData.getData().add(proxyMapper.toDto(proxyWan));
        });
        tableData.setPages(page.getTotalPages());

        return tableData;
    }

    @Override
    public boolean rebootDevice(ProxyRequest request) {
        List<License> licenseList = getListActiveProxyByLicenses(request.getUuids());
        for (License license : licenseList) {
            rebootDeviceOnePort(request, license);
        }

        return true;
    }

    void rebootDeviceOnePort(ProxyRequest request, License targetLicense) {
        // 1.1 Check license have active
        if (targetLicense.getStatus() != LicenseStatus.ACTIVE) {
            if (request.getUuids().size() == 1) {
                throw new NeoProxyServiceException(ExceptionDetail.builder()
                        .status(HttpStatus.BAD_REQUEST).errorCode(ErrorCode.NOT_HAVE_PERMISSION)
                        .message("Not have permission to reboot device when license inactive")
                        .build());
            }
            return;
        }

        // 2. Check last reboot device
        Long numberOfSecond = targetLicense.getSalePackage().getMinTimeChangeIp();
        if (targetLicense.getLastChangeIp() == null) {
            targetLicense.setLastChangeIp(LocalDateTime.now().minusYears(10));
        }
        LocalDateTime localDateTime = targetLicense.getLastChangeIp().plusSeconds(numberOfSecond);
        if (localDateTime.isAfter(LocalDateTime.now())) {
            log.error("Too fast to reboot device. Skip this license {}", targetLicense.getUuid().toString());
            if (request.getUuids().size() == 1) {
                throw new NeoProxyServiceException(ExceptionDetail.builder()
                        .status(HttpStatus.BAD_REQUEST).errorCode(ErrorCode.PROXY_TOO_FAST_TO_CHANGE_IP)
                        .message("Too fast to reboot device.")
                        .build());
            }
            return;
        }

        Proxy httpProxy = targetLicense.getHttpProxy();
        // Proxy sockProxy = targetLicense.getSockProxy();
        xProxyService.rebootPort(httpProxy.getModem(), httpProxy.getHost(), httpProxy.getSharedPort());
        log.info("[Done reboot device]----------------- : " + httpProxy.getSharedPort());
        // xProxyService.resetPort(sockProxy.getModem(), sockProxy.getHost(), sockProxy.getSharedPort());
        // log.info("[Done change ip]----------------- : " + sockProxy.getSharedPort());
    }

    @Override
    public boolean changeProxyIp(ProxyRequest request, boolean isAuto) {
        List<License> licenseList;
        if (isAuto) {
            licenseList = proxyRepository.findProxyWanByLicenses(LicenseStatus.ACTIVE, request.getUuids());
        } else {
            licenseList = getListActiveProxyByLicenses(request.getUuids());
        }
        for (License license : licenseList) {
            changeProxyIpOnePort(request, license);
        }

        return true;
    }

    @Override
    public boolean changeProxyIpOnePort(ProxyRequest request, License targetLicense) {
        // 1.1 Check license have active
        if (targetLicense.getStatus() != LicenseStatus.ACTIVE) {
            if (request.getUuids().size() == 1) {
                throw new NeoProxyServiceException(ExceptionDetail.builder()
                        .status(HttpStatus.BAD_REQUEST).errorCode(ErrorCode.NOT_HAVE_PERMISSION)
                        .message("Not have permission to change Ip when license inactive")
                        .build());
            }
            return false;
        }

        // 2. Check last change ip
        Long numberOfSecond = targetLicense.getSalePackage().getMinTimeChangeIp();
        if (targetLicense.getLastChangeIp() == null) {
            targetLicense.setLastChangeIp(LocalDateTime.now().minusYears(10));
        }
        LocalDateTime localDateTime = targetLicense.getLastChangeIp().plusSeconds(numberOfSecond);
        if (localDateTime.isAfter(LocalDateTime.now())) {
            log.error("Too fast to change Ip. Skip this license {}", targetLicense.getUuid().toString());
            if (request.getUuids().size() == 1) {
                throw new NeoProxyServiceException(ExceptionDetail.builder()
                        .status(HttpStatus.BAD_REQUEST).errorCode(ErrorCode.PROXY_TOO_FAST_TO_CHANGE_IP)
                        .message("Too fast to change IP.")
                        .build());
            }
            return false;
        }

        Proxy httpProxy = targetLicense.getHttpProxy();
        // Proxy sockProxy = targetLicense.getSockProxy();
        xProxyService.resetPort(httpProxy.getModem(), httpProxy.getHost(), httpProxy.getSharedPort());
        log.info("[Done change ip]----------------- : " + httpProxy.getSharedPort());
        // xProxyService.resetPort(sockProxy.getModem(), sockProxy.getHost(), sockProxy.getSharedPort());
        // log.info("[Done change ip]----------------- : " + sockProxy.getSharedPort());
        targetLicense.setLastChangeIp(LocalDateTime.now());
        licenseRepository.save(targetLicense);

        // 4. Update public ip
        this.syncPublicIp(httpProxy);

        return true;
    }

    @Override
    public boolean updateAuthentication(ProxyRequest request) {
        User user = authenticationService.getLoggedUser();
        List<License> licenseList = getListActiveProxyByLicenses(request.getUuids());
        log.info("Total active proxy wan for update authenticate {}", licenseList.size());

        CompletableFuture[] completableFutures = new CompletableFuture[licenseList.size()];
        AtomicInteger i = new AtomicInteger();
        licenseList.forEach(license -> {
            String authenticationUsers = null;
            String authenticationIps = null;
            if (CommonUtil.isEmpty(request.getWhiteListIps())) {
                authenticationUsers = licenseList.size() == 1 ? request.getUsername() : CommonUtil.generateAuthProxy();
            } else {
                authenticationIps = request.getWhiteListIps();
            }

            completableFutures[i.getAndIncrement()] = proxyCoreService.updateAuthentication(license, authenticationUsers, authenticationIps);

            if (license != null) {
                license.setAuthUser(authenticationUsers);
                license.setIpWhitelist(authenticationIps);
                licenseRepository.save(license);
            }
        });

        CompletableFuture<Void> combinedFuture = CompletableFuture
                .allOf(Arrays.stream(completableFutures)
                        .filter(Objects::nonNull)
                        .toArray(CompletableFuture[]::new));
        try {
            combinedFuture.join();
        } catch (CompletionException ex) {
            // NOOP
        }

        return true;
    }

    public boolean updateAuthenticationSync(ProxyRequest request) {
        try {
            List<License> licenseList = getListActiveProxyByLicenses(request.getUuids());
            log.info("Total active proxy wan for update authenticate {}", licenseList.size());

            String authenticationUsers = null;
            String authenticationIps = null;
            if (CommonUtil.isEmpty(request.getWhiteListIps())) {
                authenticationUsers = licenseList.size() == 1 ? request.getUsername() : CommonUtil.generateAuthProxy();
            } else {
                authenticationIps = request.getWhiteListIps();
            }

            for (License license : licenseList) {
                if (license != null) {
                    boolean result = proxyCoreService.updateAuthenticationSync(license, authenticationUsers, authenticationIps);
                    if (result) {
                        log.info("Update authenticate to xproxy success!");
                        license.setAuthUser(authenticationUsers);
                        license.setIpWhitelist(authenticationIps);
                        licenseRepository.save(license);
                    } else {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return false;
    }

    @Override
    public boolean updateAuthenticationLicense(License license) {
        try {
            if (license != null) {
                String authenticationUsers = null;
                String authenticationIps = null;
                if (CommonUtil.isEmpty(license.getIpWhitelist())) {
                    authenticationUsers = license.getAuthUser();
                } else {
                    authenticationIps = license.getIpWhitelist();
                }
                boolean result = proxyCoreService.updateAuthenticationSync(license, authenticationUsers, authenticationIps);
                if (result) {
                    log.info("Update authenticate to xproxy success!");
                    license.setAuthUser(authenticationUsers);
                    license.setIpWhitelist(authenticationIps);
                    licenseRepository.save(license);
                } else {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.info(e.getMessage());
        }
        return false;
    }

    @Override
    @Async
    public void updateAuthentication(Proxy proxy, String authenticationUsers, String authorizationIps) {
        try {
            List<Integer> proxyIds = new ArrayList<>();
            proxyIds.add(proxy.getXproxyId());
            ProxyInfoReq proxyInfoReq = new ProxyInfoReq(authorizationIps, authenticationUsers, proxyIds);
            boolean result = xProxyService.bulkEdit(proxy.getModem(), proxyInfoReq);
            if (result) {
                proxy.setAuthenticationUsers(authenticationUsers);
                proxy.setAuthorizationIps(authorizationIps);
                proxyRepository.save(proxy);
            }
        } catch (Exception e) {
            log.error("Error when update authenticate: " + e.getMessage());
        }
    }

    @Override
    public Integer getTotalProxy(ModemType modemType) {
        return proxyRepository.countProxy(modemType);
    }

    @Override
    public Integer getTotalAvailableProxy(ModemType modemType) {
        return proxyRepository.countAvailableProxy(ModemStatus.READY, modemType, ProxyStatus.CONNECTED);
    }

    private List<License> getListActiveProxyByLicenses(List<UUID> uuids) {
        User user = authenticationService.getLoggedUser();
        List<License> licenseList;
        if (user.isClient()) {
            licenseList = proxyRepository.findProxyWanByLicenses(user, LicenseStatus.ACTIVE, uuids);
        } else {
            licenseList = proxyRepository.findProxyWanByLicenses(LicenseStatus.ACTIVE, uuids);
        }

        log.info("Total license list {}", licenseList.size());
        if (licenseList.isEmpty()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.PROXY_ACTIVE_NOT_FOUND)
                            .message("Could not find active proxy")
                            .build());
        }
        return licenseList;
    }

    @Override
    public void deleteProxyByModem(Modem modem) {
        List<Proxy> proxyList = proxyRepository.findByModem(modem.getUuid());
        proxyRepository.deleteAll(proxyList);
    }

    @Override
    public boolean deleteProxies(ProxyRequest request) {
        request.getUuids().forEach(uuid -> {
            Proxy proxy = proxyRepository.findByUuid(uuid);
            if (proxy == null) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.PROXY_ACTIVE_NOT_FOUND)
                                .message("Could not find active proxy")
                                .build());
            }
            if (proxy.getPortType().equals(PortType.HTTP) && proxy.getLicense() != null) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.PROXY_ACTIVE_NOT_FOUND)
                                .message("The port in Used cannot be removed, please make sure to only remove the ports that are Available.")
                                .build());
            }
            if (proxy.getPortType().equals(PortType.SocksV5) && proxy.getLicenseSock() != null) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.PROXY_ACTIVE_NOT_FOUND)
                                .message("The port in Used cannot be removed, please make sure to only remove the ports that are Available.")
                                .build());
            }

            if (proxy.getPortType().equals(PortType.HTTP) && proxy.getBrotherPort() != null) {
                Optional<Proxy> sockPortOptional = proxyRepository.findBySharedPort(proxy.getModem(), proxy.getBrotherPort(), PortType.SocksV5);
                if (sockPortOptional.isPresent()) {
                    Proxy sockPort = sockPortOptional.get();
                    if (sockPort.getPortType().equals(PortType.SocksV5) && proxy.getLicenseSock() != null) {
                        throw new NeoProxyServiceException(
                                ExceptionDetail.builder()
                                        .status(HttpStatus.BAD_REQUEST)
                                        .errorCode(ErrorCode.PROXY_ACTIVE_NOT_FOUND)
                                        .message("The port in Used cannot be removed, please make sure to only remove the ports that are Available.")
                                        .build());
                    }
                    proxyRepository.delete(sockPort);
                }
            }
            proxyRepository.delete(proxy);
        });
        return true;
    }

    @Override
    public List<Proxy> getAvailableProxiesByPackage(Package salePackage) {
        String ispRequest = salePackage.getIsp();
        String locationRequest = salePackage.getLocation();

        if (CommonUtil.isEmpty(ispRequest) || CommonUtil.isEmpty(locationRequest)) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LOCATION_NOT_FOUND)
                            .message("The isp or location of package is required")
                            .build());
        }

        // 2. Count proxy by modem
        List<Proxy> httpProxyList;
        if (!CommonUtil.isEmpty(ispRequest)) {
            if (!CommonUtil.isEmpty(locationRequest)) {
                log.info("Find sale proxy by ISP Location {} {}", ispRequest, locationRequest);
                httpProxyList = proxyRepository.findAvailableProxyByIspAndPositions(PortType.HTTP, ispRequest, locationRequest, ProxyStatus.CONNECTED);
            } else {
                log.info("Find sale proxy by ISP {}", ispRequest);
                httpProxyList = proxyRepository.findAvailableProxyByIsp(PortType.HTTP, ispRequest, ProxyStatus.CONNECTED);
            }
        } else if (!CommonUtil.isEmpty(locationRequest)) {
            log.info("Find sale proxy by location {}", locationRequest);
            httpProxyList = proxyRepository.findAvailableProxyByLocation(locationRequest, PortType.HTTP, ProxyStatus.CONNECTED);
        } else {
            // Throw not found
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.PROXY_NOT_ENOUGH)
                            .message("The quantity of proxy was not enough")
                            .build());
        }
        return httpProxyList;
    }

    @Override
    public List<Proxy> getAvailableProxiesByLocationAndIsp(String locationRequest, String ispRequest) {
        if (CommonUtil.isEmpty(ispRequest) || CommonUtil.isEmpty(locationRequest)) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.LOCATION_NOT_FOUND)
                            .message("The isp or location of package is required")
                            .build());
        }

        // 2. Count proxy by modem
        List<Proxy> httpProxyList;
        if (!CommonUtil.isEmpty(ispRequest)) {
            log.info("Find sale proxy by ISP {}", ispRequest);

            if (!CommonUtil.isEmpty(locationRequest)) {
                httpProxyList = proxyRepository.findAvailableProxyByIspAndPositions(PortType.HTTP, ispRequest, locationRequest, ProxyStatus.CONNECTED);
            } else {
                httpProxyList = proxyRepository.findAvailableProxyByIsp(PortType.HTTP, ispRequest, ProxyStatus.CONNECTED);
            }
        } else if (!CommonUtil.isEmpty(locationRequest)) {
            log.info("Find sale proxy by location {}", locationRequest);
            httpProxyList = proxyRepository.findAvailableProxyByLocation(locationRequest, PortType.HTTP, ProxyStatus.CONNECTED);
        } else {
            // Throw not found
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.PROXY_NOT_ENOUGH)
                            .message("The quantity of proxy was not enough")
                            .build());
        }
        return httpProxyList;
    }

    @Override
    public boolean updateSaleStatusProxy(Proxy proxy) {
        proxy.setSaleStatus(ProxySaleStatus.AVAILABLE);
        proxy.setCounterUlUsedBytes(0L);
        proxy.setCounterDlUsedBytes(0L);
        proxy.setCounterAllUsedBytes(0L);
        proxy.setAuthenticationUsers(null);
        proxy.setAuthorizationIps(null);
        proxyRepository.save(proxy);

        xProxyService.resetDataCounter(proxy.getModem(), new ResetDataCounterReq(List.of(proxy.getXproxyId())));
        return true;
    }

    @Override
    public boolean syncPublicIp(@NonNull Proxy proxy) {
        Modem modem = proxy.getModem();
        if (!modem.getStatus().equals(ModemStatus.READY)) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.MODEM_NOT_ACTIVE)
                            .message("Modem not active")
                            .build());
        }

        ModemRequest modemRequest = ModemRequest.builder()
                .userName(modem.getUserName())
                .password(modem.getPassword())
                .domain(modem.getDomain())
                .build();

        List<XProxyWanInfo> listDeviceInfo = xProxyService.getListProviderInfo(modemRequest);
        XProxyWanInfo proxyWanInfo = listDeviceInfo
                .stream()
                .filter(item -> item.getPosition().equals(proxy.getXproxyPosition())).findFirst().orElse(null);
        if (proxyWanInfo != null && !CommonUtil.isEmpty(proxyWanInfo.getProvider())) {
            if (proxyWanInfo.getPublic_ip().equals("CONNECT_INTERNET_ERROR")) {
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                return syncPublicIp(proxy);
            }

            proxy.setIsp(proxyWanInfo.getProvider());
            proxy.setPublicIp(proxyWanInfo.getPublic_ip());
            proxyRepository.save(proxy);
        }

        return true;
    }


    @Override
    public boolean changeProxyLocation(ProxyRequest request) {
        List<License> licenseList = getListActiveProxyByLicenses(request.getUuids());
        if (licenseList.size() == 1) {
            changeLocationOfLicense(request, licenseList.get(0));
        } else {
            for (License license : licenseList) {
                changeLocationOfLicense(request, license);
            }
        }

        return true;
    }

    @Transactional
    private void changeLocationOfLicense(ProxyRequest request, License targetLicense) {
        User user = authenticationService.getLoggedUser();
        
        // 1.0 Check if user has permission to change location
        Package salePackage = targetLicense.getSalePackage();
        if (salePackage.getAllowChangeLocation() == null || salePackage.getAllowChangeLocation() == 0) {
            throw new NeoProxyServiceException(ExceptionDetail.builder()
                    .status(HttpStatus.BAD_REQUEST)
                    .errorCode(ErrorCode.NOT_HAVE_PERMISSION)
                    .message("This package does not allow location changes")
                    .build());
        }

        // 2.0 Check location fee and user balance
        BigDecimal locationFee = salePackage.getLocationFee();
        if (locationFee != null && locationFee.compareTo(BigDecimal.ZERO) > 0) {
            // Check if user has sufficient balance
            if (user.getBalance().compareTo(locationFee) < 0) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.INSUFFICIENT_BALANCE)
                                .message("Insufficient balance to change location. Required: " + locationFee + ", Available: " + user.getBalance())
                                .build());
            }

            // Create transaction for location change fee
            Transaction transaction = Transaction.builder()
                    .customer(user)
                    .amount(locationFee)
                    .currency("USD")
                    .type(TransactionType.PURCHASE)
                    .status(TransactionStatus.COMPLETED)
                    .description("Location change fee for license: " + targetLicense.getUuid())
                    .note("Location Change Fee")
                    .discount(BigDecimal.ZERO)
                    .build();
            transactionRepository.save(transaction);

            // Update user balance
            user.setBalance(user.getBalance().subtract(locationFee));
            userRepository.save(user);

            log.info("Location change fee {} deducted from user {} balance for license {}", 
                    locationFee, user.getUuid(), targetLicense.getUuid());
        }

        /**
         * Check Location
         */
        if (!CommonUtil.isEmpty(request.getLocation())) {
            targetLicense.setLocation(request.getLocation());
            targetLicense.setLastChangeLocation(LocalDateTime.now());
        } else {
            targetLicense.setLocation("");
            targetLicense.setLastChangeLocation(LocalDateTime.now());
        }

        /**
         * Find new proxy by package
         */
        Optional<Proxy> newProxyOptional = findNewProxyByLocation(targetLicense.getLocation());
        if (newProxyOptional.isEmpty()) {
            throw new NeoProxyServiceException(ExceptionDetail.builder()
                    .status(HttpStatus.BAD_REQUEST).errorCode(ErrorCode.PROXY_ACTIVE_NOT_FOUND)
                    .message("The selected location does not have the available proxy. Please choose another location!")
                    .build());
        }

        /**
         * Clear authentication for old proxy
         */
        Proxy oldHttpProxy = targetLicense.getHttpProxy();
        if (oldHttpProxy != null) {
            if (oldHttpProxy.getModem().getStatus() == ModemStatus.READY) {
                String newAuthenReset = CommonUtil.generateAuthProxy();
                List<Integer> proxyIds = new ArrayList<>();

                proxyIds.add(oldHttpProxy.getXproxyId());
                oldHttpProxy.setSaleStatus(ProxySaleStatus.AVAILABLE);
                oldHttpProxy.setAuthenticationUsers(newAuthenReset);
                oldHttpProxy.setAuthorizationIps(null);
                proxyRepository.save(oldHttpProxy);
                targetLicense.setHttpProxy(null);


                Proxy oldSockProxy = targetLicense.getSockProxy();
                if (oldSockProxy != null) {
                    proxyIds.add(oldSockProxy.getXproxyId());
                    oldSockProxy.setSaleStatus(ProxySaleStatus.AVAILABLE);
                    oldSockProxy.setAuthenticationUsers(newAuthenReset);
                    oldSockProxy.setAuthorizationIps(null);
                    proxyRepository.save(oldSockProxy);
                    targetLicense.setSockProxy(null);
                }

                ProxyInfoReq proxyInfoReq = new ProxyInfoReq("", newAuthenReset, proxyIds);
                xProxyService.bulkEditAsync(oldHttpProxy.getModem(), proxyInfoReq);
            }
        }

        /**
         * Start Assign new proxy
         */
        Proxy httpProxy = newProxyOptional.get();
        int sockPortMatch = httpProxy.getBrotherPort();
        log.info("_____Brother port of http port {} with sock port: {}", httpProxy.getSharedPort(), sockPortMatch);
        Optional<Proxy> sockProxy = proxyRepository.findBySharedPort(httpProxy.getModem(), sockPortMatch, PortType.SocksV5);

        // Update new http proxy/sock proxy into current license
        targetLicense.setHttpProxy(httpProxy);
        sockProxy.ifPresent(targetLicense::setSockProxy);
        licenseRepository.save(targetLicense);

        List<Integer> resetDataIds = new ArrayList<>();
        // Mark httpProxy used
        httpProxy.setProxyToSale();
        proxyRepository.save(httpProxy);
        updateAuthentication(httpProxy, targetLicense.getAuthUser(), targetLicense.getIpWhitelist());
        resetDataIds.add(httpProxy.getXproxyId());

        // Mark sockProxy used
        sockProxy.ifPresent((sockP) -> {
            sockP.setProxyToSale();
            proxyRepository.save(sockP);
            updateAuthentication(sockP, targetLicense.getAuthUser(), targetLicense.getIpWhitelist());
            resetDataIds.add(sockP.getXproxyId());
        });

        // Reset data counter
        xProxyService.resetDataCounter(httpProxy.getModem(), new ResetDataCounterReq(resetDataIds));

        /**
         * End Assign new proxy
         */
    }

    private Optional<Proxy> findNewProxyByLocation(String location) {
        List<Proxy> httpProxyList = proxyRepository.findAvailableProxyByLocation(location, PortType.HTTP, ProxyStatus.CONNECTED);
        if (httpProxyList.isEmpty()) {
            return Optional.empty();
        }

        int randomItem = rand.nextInt(httpProxyList.size());
        return Optional.ofNullable(httpProxyList.get(randomItem));
    }
}
