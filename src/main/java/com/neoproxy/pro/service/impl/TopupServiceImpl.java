package com.neoproxy.pro.service.impl;

import com.neoproxy.pro.config.AppConf;
import com.neoproxy.pro.config.Constants;
import com.neoproxy.pro.domain.Transaction;
import com.neoproxy.pro.domain.User;
import com.neoproxy.pro.dto.ExceptionDetail;
import com.neoproxy.pro.dto.TopupDto;
import com.neoproxy.pro.dto.TopupRequest;
import com.neoproxy.pro.enums.ErrorCode;
import com.neoproxy.pro.enums.MonitorType;
import com.neoproxy.pro.enums.TransactionStatus;
import com.neoproxy.pro.enums.TransactionType;
import com.neoproxy.pro.mail.EmailDetails;
import com.neoproxy.pro.mail.EmailService;
import com.neoproxy.pro.nowpayments.model.PaymentReq;
import com.neoproxy.pro.nowpayments.model.PaymentResp;
import com.neoproxy.pro.nowpayments.model.PaymentStatusResp;
import com.neoproxy.pro.nowpayments.service.NowPaymentService;
import com.neoproxy.pro.repository.TransactionRepository;
import com.neoproxy.pro.repository.UserRepository;
import com.neoproxy.pro.service.*;
import com.neoproxy.pro.service.exception.NeoProxyServiceException;
import com.neoproxy.pro.utils.CommonUtil;
import com.neoproxy.pro.utils.DateUtil;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class TopupServiceImpl implements TopupService {
    AuthenticationService authenticationService;
    NowPaymentService nowPaymentService;
    TransactionRepository transactionRepository;
    UserRepository userRepository;
    ConfigurationService configurationService;
    AppConf appConf;
    EmailService emailService;
    MonitorService monitorService;
    TransactionService transactionService;
    OrderV2Service orderV2Service;

    @Override
    public TopupDto createTopup(TopupRequest topupRequest) {
        User user = authenticationService.getLoggedUser();

        // 0. Validation
        if (topupRequest.getPriceAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.INSUFFICIENT_BALANCE)
                            .message("INSUFFICIENT_BALANCE")
                            .build());
        }

        // 1. Create topup transaction
        String description = "TOPUP " + topupRequest.getPriceAmount() + " " + topupRequest.getPriceCurrency();
        Transaction transaction = Transaction.builder()
                .customer(user)
                .amount(topupRequest.getPriceAmount())
                .currency(topupRequest.getPriceCurrency())
                .type(TransactionType.TOPUP)
                .status(TransactionStatus.PROCESSING)
                .note("")
                .build();
        transactionRepository.save(transaction);

        // 2. Create payment in nowpayment
        PaymentReq paymentReq = PaymentReq.builder()
                .priceAmount(topupRequest.getPriceAmount())
                .priceCurrency(topupRequest.getPriceCurrency())
                .payCurrency(topupRequest.getPayCurrency())
                .orderId(transaction.getUuid().toString())
                .orderDescription(description)
                .ipnCallbackUrl("https://aaaproxy.io")
                .build();

        PaymentResp paymentResp = null;
        try {
            paymentResp = nowPaymentService.createPayment(paymentReq);
        } catch (Exception e) {
            // Error when payment
            transaction.setNote(e.getMessage());
            transaction.setDescription(e.getMessage());
            transaction.setStatus(TransactionStatus.FAILED);
            transactionRepository.save(transaction);

            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.PAYMENT_SERVER_ERROR)
                            .message("Payment server error! Please try later!")
                            .build());
        }

        if (!CommonUtil.isEmpty(paymentResp)
                && !CommonUtil.isEmpty(paymentResp.getPaymentId())
                && !CommonUtil.isEmpty(paymentResp.getPayAddress())) {
            transaction.setReferenceId(paymentResp.getPaymentId());
            transaction.setPayAddress(paymentResp.getPayAddress());
            transaction.setPayCurrency(paymentResp.getPayCurrency().toUpperCase());
            transaction.setPayAmount(paymentResp.getPayAmount());
            String note = String.format("Deposit Address: %s <br/> Amount: %s <br/> Coin: %s", paymentResp.getPayAddress(), paymentResp.getPayAmount(), paymentResp.getPayCurrency().toUpperCase());
            transaction.setNote(note);
            transaction.setDescription(description + " (NOWPayments payment_id: " + paymentResp.getPaymentId() + ")");
            transactionRepository.save(transaction);

            return TopupDto.builder()
                    .payAddress(paymentResp.getPayAddress())
                    .paymentId(paymentResp.getPaymentId())
                    .payAmount(paymentReq.getPriceAmount())
                    .payCurrency(paymentReq.getPayCurrency())
                    .build();
        } else {
            transactionRepository.delete(transaction);
        }

        throw new NeoProxyServiceException(
                ExceptionDetail.builder()
                        .status(HttpStatus.BAD_REQUEST)
                        .errorCode(ErrorCode.PAYMENT_SERVER_ERROR)
                        .message("Payment server error! Please try later!")
                        .build());
    }

    @Override
    public void updateTopupStatus(Transaction transaction) {
        if (!CommonUtil.isEmpty(transaction.getReferenceId())) {
            // 1.0 Check status in nowpayment
            PaymentStatusResp paymentStatusResp = nowPaymentService.getPaymentStatus(transaction.getReferenceId());
            if (paymentStatusResp != null) {
                switch (paymentStatusResp.getPaymentStatus()) {
                    case "partially_paid": {
                        // Update balance
                        // Update transaction complete
                        transaction.setStatus(TransactionStatus.PARTIALLY_PAID);
                        transactionRepository.save(transaction);
                    }
                    break;
                    case "finished": {
                        // Update balance
                        User user = transaction.getCustomer();
                        user.setBalance(user.getBalance().add(paymentStatusResp.getPriceAmount()));
                        userRepository.save(user);

                        // Update transaction complete
                        transaction.setStatus(TransactionStatus.COMPLETED);
                        if (paymentStatusResp.getPriceAmount().compareTo(transaction.getAmount()) > 0) {
                            transaction.setAmount(paymentStatusResp.getPriceAmount());
                            transaction.setDescription(transaction.getDescription() + "| From crypto: " + paymentStatusResp.getPriceAmount());
                        }
                        transactionRepository.save(transaction);

                        handleTopupSuccess(user, paymentStatusResp, paymentStatusResp.getPriceAmount());
                    }
                    break;
                    case "expired": {
                        transaction.setStatus(TransactionStatus.EXPIRED);
                        transactionRepository.save(transaction);
                    }
                    break;
                    case "failed":
                    case "refunded": {
                        transaction.setStatus(TransactionStatus.FAILED);
                        transactionRepository.save(transaction);
                    }
                    break;
                }
            }
        } else {
            transaction.setStatus(TransactionStatus.FAILED);
            transactionRepository.save(transaction);
        }
    }


    @Override
    public void updatePurchaseStatus(Transaction transaction) {
        // ONLY UPDATE FOR NOWPAYMENT METHOD
        if (!CommonUtil.isEmpty(transaction.getReferenceId())) {
            // 1.0 Check status in nowpayment
            PaymentStatusResp paymentStatusResp = nowPaymentService.getPaymentStatus(transaction.getReferenceId());
            if (paymentStatusResp != null) {
                switch (paymentStatusResp.getPaymentStatus()) {
                    case "partially_paid": {
                        // Update balance
                        // Update transaction complete
                        transaction.setStatus(TransactionStatus.PARTIALLY_PAID);
                        transactionRepository.save(transaction);
                    }
                    break;
                    case "finished": {
                        orderV2Service.handleConfirmOrder(transaction);
                    }
                    break;
                    case "expired": {
                        transaction.setStatus(TransactionStatus.EXPIRED);
                        transactionRepository.save(transaction);
                    }
                    break;
                    case "failed":
                    case "refunded": {
                        transaction.setStatus(TransactionStatus.FAILED);
                        transactionRepository.save(transaction);
                    }
                    break;
                }
            }
        } else {
            LocalDateTime checkTime = LocalDateTime.now().minusHours(1);
            if (transaction.getCreatedAt().isBefore(checkTime)) {
                transaction.setStatus(TransactionStatus.FAILED);
                transaction.setNote("Transaction over 1 hour time limit");
                transactionRepository.save(transaction);
            }
        }
    }

    public static void main(String[] args) {
        LocalDateTime createdAt = DateUtil.convertIsoStringToDate("2025-05-22T04:10:00.000Z");
        LocalDateTime checkTime = LocalDateTime.now().minusHours(1);
        if (createdAt.isBefore(checkTime)) {
            System.out.println("Transaction over 1 hour time limit");
        } else {
            System.out.println("Transaction waiting payment");
        }
    }

    private void handleTopupSuccess(User user, PaymentStatusResp paymentStatusResp, BigDecimal amount) {
        notificationUserTopupForAdmin(user, paymentStatusResp.getOutcomeAmount(), paymentStatusResp.getPayCurrency());
        notificationUserTopupForUser(user, paymentStatusResp.getOutcomeAmount(), paymentStatusResp.getPayCurrency());
        // Check affiliate
        transactionService.handleAffiliate(amount, user);
        log.info("_____ COMPLETE TOPUP OF USER {} WITH AMOUNT {}", user.getName(), amount);
    }

    private void notificationUserTopupForAdmin(User user, BigDecimal convertAmount, String currency) {
        String notificationEmail = emailService.getAdminEmail();
        if (!CommonUtil.isEmpty(notificationEmail)) {
            try {
                String finalMail = Constants.EMAIL_ADMIN_TOPUP_SUCCESS
                        .replace("#NAME", user.getName())
                        .replace("#EMAIL", user.getEmail())
                        .replace("#AMOUNT", CommonUtil.formatMoney(convertAmount))
                        .replace("#PAYGATE", "NowPayment " + currency)
                        .replace("#CREATED_DATE", LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")));

                EmailDetails emailDetails = EmailDetails.builder()
                        .subject(appConf.getName() + " - Customer topup successful")
                        .msgBody(finalMail)
                        .recipient(notificationEmail)
                        .build();
                String result = emailService.sendSimpleMail(emailDetails);
                log.info("SEND EMAIL: {}", result);
            } catch (Exception e) {
                log.error("_____ ERROR WHEN SEND MAIL", e);
            }
        }

        monitorService.addAlertMonitor(
                MonitorType.TOPUP,
                user.getEmail(),
                user.getEmail(),
                CommonUtil.formatMoney(convertAmount),
                "NowPayment " + currency
        );
    }

    private void notificationUserTopupForUser(User user, BigDecimal convertAmount, String currency) {
        String notificationEmail = user.getEmail();
        if (!CommonUtil.isEmpty(notificationEmail) && user.getReminder().equals(1)) {
            try {
                String finalMail = Constants.EMAIL_USER_TOPUP_SUCCESS
                        .replace("#NAME", user.getName())
                        .replace("#AMOUNT", CommonUtil.formatMoney(convertAmount))
                        .replace("#PAYGATE", "NowPayment " + currency)
                        .replace("#CREATED_DATE", LocalDateTime.now().format(DateTimeFormatter.ofPattern("MM/dd/yyyy")));

                EmailDetails emailDetails = EmailDetails.builder()
                        .subject(appConf.getName() + " - Topup successful")
                        .msgBody(finalMail)
                        .recipient(notificationEmail)
                        .build();
                String result = emailService.sendSimpleMail(emailDetails);
                log.info("SEND EMAIL: {}", result);
            } catch (Exception e) {
                log.error("_____ ERROR WHEN SEND MAIL", e);
            }
        }
    }

}
