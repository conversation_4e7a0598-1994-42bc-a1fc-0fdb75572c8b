package com.neoproxy.pro.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.neoproxy.pro.config.AppConf;
import com.neoproxy.pro.config.Constants;
import com.neoproxy.pro.domain.Package;
import com.neoproxy.pro.domain.*;
import com.neoproxy.pro.dto.*;
import com.neoproxy.pro.enums.*;
import com.neoproxy.pro.mail.EmailDetails;
import com.neoproxy.pro.mail.EmailService;
import com.neoproxy.pro.mapper.PackageMapper;
import com.neoproxy.pro.mapper.TransactionMapper;
import com.neoproxy.pro.nowpayments.model.PaymentReq;
import com.neoproxy.pro.nowpayments.model.PaymentResp;
import com.neoproxy.pro.nowpayments.service.NowPaymentService;
import com.neoproxy.pro.repository.*;
import com.neoproxy.pro.service.AuthenticationService;
import com.neoproxy.pro.service.PromotionService;
import com.neoproxy.pro.service.ProxyService;
import com.neoproxy.pro.service.TransactionV2Service;
import com.neoproxy.pro.service.exception.NeoProxyServiceException;
import com.neoproxy.pro.stripe.service.StripeService;
import com.neoproxy.pro.utils.CommonUtil;
import com.neoproxy.pro.utils.JsonUtil;
import com.neoproxy.pro.xproxy.model.PortType;
import com.neoproxy.pro.xproxy.model.ResetDataCounterReq;
import com.neoproxy.pro.xproxy.service.XProxyService;
import lombok.AccessLevel;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class TransactionV2ServiceImpl implements TransactionV2Service {
    AuthenticationService authenticationService;
    TransactionRepository transactionRepository;
    PackageRepository packageRepository;
    ProxyRepository proxyRepository;
    LicenseRepository licenseRepository;
    UserRepository userRepository;
    NowPaymentService nowPaymentService;
    PromotionService promotionService;
    EmailService emailService;
    AppConf appConf;
    ProxyService proxyService;
    XProxyService xProxyService;
    TransactionMapper transactionMapper;
    StripeService stripeService;
    PackageMapper packageMapper;

    @Override
    @Transactional
    public TransactionDto checkoutOrder(@NonNull OrderProxyV2Request request) throws JsonProcessingException {
        User user = authenticationService.getLoggedUser();
        BigDecimal discountAmount = BigDecimal.ZERO;

        Package salePackage = packageRepository.findByUuid(request.getPackageUuid());
        // 1.0 Check available port quantity
        List<Proxy> httpProxyList = proxyService.getAvailableProxiesByPackage(salePackage);
        if (request.getQuantity() > httpProxyList.size()) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.PROXY_NOT_ENOUGH)
                            .message("The quantity of proxy was not enough")
                            .build());
        }

        BigDecimal totalAmount = salePackage.getPrice()
                .multiply(new BigDecimal(request.getQuantity()))
                .multiply(new BigDecimal(request.getTime()));

        // 1.1 ADD VPN FEE
        if (!CommonUtil.isEmpty(request.getVpnType()) && !CommonUtil.isEmpty(salePackage.getVpnFee())) {
            totalAmount = totalAmount.add(salePackage.getVpnFee()
                                            .multiply(new BigDecimal(request.getQuantity())));
            log.info("---- ADD VPN FEE {} - TOTAL AMOUNT {}", salePackage.getVpnFee(), totalAmount);
        }

        String ispRequest = salePackage.getIsp();
        String locationRequest = salePackage.getLocation();
        log.info("Place order with total amount {}, isp {}, location {}", totalAmount, ispRequest, locationRequest);

        // 1.2 CHECK VOUCHER AMOUNT
        if (CommonUtil.isNotEmpty(request.getPromotionCode())) {
            BigDecimal discount = promotionService.redeemCode(request.getPromotionCode(), totalAmount);
            discountAmount = discountAmount.add(discount);
            log.info("---- PROMOTION {} - DISCOUNT AMOUNT {}", request.getPromotionCode(), discount);
            totalAmount = totalAmount.subtract(discount);
        }

        // 1.3 CHECK DURATION PROMOTION AMOUNT
        if (CommonUtil.isNotEmpty(request.getDurationPromotionId())) {
            BigDecimal discount = promotionService.getDiscountByPromotion(request.getDurationPromotionId(), totalAmount);
            discountAmount = discountAmount.add(discount);
            log.info("---- PROMOTION ID {} - DISCOUNT AMOUNT {}", request.getDurationPromotionId(), discount);
            totalAmount = totalAmount.subtract(discount);
        }

        // 1.4 CHECK QUANTITY PROMOTION AMOUNT
        if (CommonUtil.isNotEmpty(request.getQuantityPromotionId())) {
            BigDecimal discount = promotionService.getDiscountByPromotion(request.getQuantityPromotionId(), totalAmount);
            discountAmount = discountAmount.add(discount);
            log.info("---- PROMOTION ID {} - DISCOUNT AMOUNT {}", request.getQuantityPromotionId(), discount);
            totalAmount = totalAmount.subtract(discount);
        }

        // 1.5 Compare Display amount vs System calculate
        if (!request.getTotalAmount().setScale(2, RoundingMode.HALF_UP).equals(totalAmount.setScale(2, RoundingMode.HALF_UP))) {
            throw new NeoProxyServiceException(
                    ExceptionDetail.builder()
                            .status(HttpStatus.BAD_REQUEST)
                            .errorCode(ErrorCode.WRONG_ORDER_AMOUNT)
                            .message("Wrong order amount. Please contact admin!")
                            .build());
        }

        // 1.6 Check user balance
        if (totalAmount.compareTo(BigDecimal.ZERO) <= 0 || totalAmount.compareTo(user.getBalance()) > 0) {
            if (request.getPaymentMethod() == null) {
                throw new NeoProxyServiceException(
                        ExceptionDetail.builder()
                                .status(HttpStatus.BAD_REQUEST)
                                .errorCode(ErrorCode.INSUFFICIENT_BALANCE)
                                .message("Account has insufficient balance. Please select payment methods")
                                .build());
            }

            return handleWithInsufficientBalance(
                    user,
                    request,
                    salePackage,
                    totalAmount,
                    discountAmount
            );
        } else {
            return handleWithEnoughBalance(
                    user,
                    request,
                    salePackage,
                    totalAmount,
                    discountAmount
            );
        }

    }

    private TransactionDto handleWithInsufficientBalance(User user, OrderProxyV2Request request, Package salePackage, BigDecimal finalAmount, BigDecimal discountAmount) throws JsonProcessingException {
        // 5. Create purchase transaction
        String requestSelected = JsonUtil.toString(request);
        PackageDto packageDto = packageMapper.toDto(salePackage);
        String salePkgSelected = JsonUtil.toString(packageDto);
        Transaction transaction = Transaction.builder()
                .customer(user)
                .amount(finalAmount)
                .currency("USD")
                .type(TransactionType.PURCHASE)
                .status(TransactionStatus.AWAITING_PAYMENT)
                .description(requestSelected)
                .note(salePkgSelected)
                .discount(discountAmount)
                .promoCode(request.getPromotionCode())
                .extra(salePkgSelected)
                .build();
        log.info("------------------------");
        log.info(transaction.toString());
        transaction = transactionRepository.save(transaction);
        transactionRepository.flush();

        TransactionDto transactionDto = transactionMapper.toDto(transaction);
        transactionDto.setPaymentMethod(request.getPaymentMethod().getName());

        // 5.1 Handle payment with STRIPE
        if (PaymentMethodType.STRIPE.equals(request.getPaymentMethod())) {
            String paymentUrl = stripeService.createCheckoutSession(finalAmount, transaction.getUuid().toString(), "/user/proxy", Constants.PAYMENT_PURCHASE);
            transactionDto.setPaymentUrl(paymentUrl);
            /* Callback from Stripe will update status update order */
            return transactionDto;
        }

        // 5.1 Handle payment with NOW_PAYMENT
        if (PaymentMethodType.NOW_PAYMENT.equals(request.getPaymentMethod())) {
            String description = "Payment for #orderid " + transaction.getId();
            PaymentReq paymentReq = PaymentReq.builder()
                    .priceAmount(finalAmount)
                    .priceCurrency(request.getPriceCurrency())
                    .payCurrency(request.getPayCurrency())
                    .orderId(transaction.getUuid().toString())
                    .orderDescription(description)
                    .ipnCallbackUrl("https://aaaproxy.io")
                    .build();

            PaymentResp paymentResp = null;
            try {
                paymentResp = nowPaymentService.createPayment(paymentReq);
            } catch (Exception e) {
                // Error when payment
                throwWhenErrorPayment(transaction, e);
                return transactionDto;
            }

            if (!CommonUtil.isEmpty(paymentResp)
                    && !CommonUtil.isEmpty(paymentResp.getPaymentId())
                    && !CommonUtil.isEmpty(paymentResp.getPayAddress())) {
                transaction.setReferenceId(paymentResp.getPaymentId());
                transaction.setPayAddress(paymentResp.getPayAddress());
                transaction.setPayCurrency(paymentResp.getPayCurrency().toUpperCase());
                transaction.setPayAmount(paymentResp.getPayAmount());
                transactionRepository.save(transaction);

                TopupDto topupDto = TopupDto.builder()
                        .amount(finalAmount)
                        .payAddress(paymentResp.getPayAddress())
                        .paymentId(paymentResp.getPaymentId())
                        .payAmount(paymentReq.getPriceAmount())
                        .payCurrency(paymentReq.getPayCurrency())
                        .type(TransactionType.PURCHASE.name())
                        .build();
                transactionDto.setTopupDto(topupDto);
                return transactionDto;
            } else {
                throwWhenErrorPayment(transaction, new Exception("Error from server response!"));
                return transactionDto;
            }
        }

        throwWhenErrorPayment(transaction, new Exception("Payment not found! Please try again!"));
        return transactionDto;
    }

    private void throwWhenErrorPayment(Transaction transaction, Exception e) {
        transaction.setNote(e.getMessage());
        transaction.setDescription(e.getMessage());
        transaction.setStatus(TransactionStatus.FAILED);
        transactionRepository.save(transaction);

        throw new NeoProxyServiceException(
                ExceptionDetail.builder()
                        .status(HttpStatus.BAD_REQUEST)
                        .errorCode(ErrorCode.PAYMENT_SERVER_ERROR)
                        .message("Payment server error! Please try later!")
                        .build());
    }

    private TransactionDto handleWithEnoughBalance(User user, OrderProxyV2Request request, Package salePackage, BigDecimal finalAmount, BigDecimal discountAmount) {
        // 5. Create purchase transaction
        List<String> transactionContent = new ArrayList();
        Transaction transaction = Transaction.builder()
                .customer(user)
                .amount(finalAmount)
                .currency("USD")
                .type(TransactionType.PURCHASE)
                .status(TransactionStatus.COMPLETED)
                .description("")
                .note("")
                .discount(discountAmount)
                .promoCode(request.getPromotionCode())
                .build();
        transaction = transactionRepository.save(transaction);


        List<Proxy> httpProxyList = proxyService.getAvailableProxiesByPackage(salePackage);
        // 6. Create license active
        List<Proxy> orderProxyList = httpProxyList.subList(0, request.getQuantity());
        List<License> activeLicenseList = new ArrayList<>();
        for (Proxy httpProxy : orderProxyList) {
            // 6.1 Create active license
            String authenticationUsers = null;
            String authenticationIps = null;
            if (CommonUtil.isEmpty(request.getWhiteListIps())) {
                authenticationUsers = CommonUtil.isEmpty(request.getUsername()) ? CommonUtil.generateAuthProxy() : request.getUsername();
            } else {
                authenticationIps = request.getWhiteListIps();
            }

            int sockPortMatch = httpProxy.getBrotherPort();
            log.info("_____Brother port of http port {} with sock port: {}", httpProxy.getSharedPort(), sockPortMatch);
            Optional<Proxy> sockProxy = proxyRepository.findBySharedPort(httpProxy.getModem(), sockPortMatch, PortType.SocksV5);

            // 6.2 Create license
            String ispRequest = salePackage.getIsp();
            String locationRequest = salePackage.getLocation();
            License license = License.builder()
                    .startDate(LocalDateTime.now())
                    .status(LicenseStatus.ACTIVE)
                    .customer(user)
                    .httpProxy(httpProxy)
                    .salePackage(salePackage)
                    .transaction(transaction)
                    .authUser(authenticationUsers)
                    .ipWhitelist(authenticationIps)
                    .location(locationRequest)
                    .isp(ispRequest)
                    .modemType(httpProxy.getModemType())
                    .vpnType(request.getVpnType())
                    .build();
            sockProxy.ifPresent(license::setSockProxy);

            if (salePackage.getPackageUnit() == PackageUnit.WEEK) {
                license.setExpiredDate(LocalDateTime.now().plusDays(7L * salePackage.getDuration() * request.getTime()));
            } else if (salePackage.getPackageUnit() == PackageUnit.MONTH) {
                license.setExpiredDate(LocalDateTime.now().plusMonths((long) salePackage.getDuration() * request.getTime()));
            } else if (salePackage.getPackageUnit() == PackageUnit.MINUTES) {
                license.setExpiredDate(LocalDateTime.now().plusMinutes((long) salePackage.getDuration() * request.getTime()));
            } else {
                license.setExpiredDate(LocalDateTime.now().plusDays((long) salePackage.getDuration() * request.getTime()));
            }
            licenseRepository.save(license);
            activeLicenseList.add(license);
            transactionContent.add(license.getUuid().toString());

            // 6.2 Mark httpProxy used
            List<Integer> resetDataIds = new ArrayList<>();
            httpProxy.setProxyToSale();
            proxyRepository.save(httpProxy);
            proxyService.updateAuthentication(httpProxy, license.getAuthUser(), license.getIpWhitelist());
            resetDataIds.add(httpProxy.getXproxyId());

            // 6.4 Mark sockProxy used
            sockProxy.ifPresent((sockP) -> {
                sockP.setProxyToSale();
                proxyRepository.save(sockP);
                proxyService.updateAuthentication(sockP, license.getAuthUser(), license.getIpWhitelist());
                resetDataIds.add(sockP.getXproxyId());
            });
            // 6.4 Reset data counter
            xProxyService.resetDataCounter(httpProxy.getModem(), new ResetDataCounterReq(resetDataIds));

            // 6.5 Enable OpenVPN as default.
            if (request.getVpnType() != null)
                xProxyService.enableVpn(license.getHttpProxy().getModem(), license.getHttpProxy().getXproxyPosition(), request.getVpnType().getXproxyType(), true);
        }

        transaction.setDescription("[V2] License: " + String.join(",", transactionContent));
        transactionRepository.save(transaction);

        // 7. Update user balance
        user.setBalance(user.getBalance().subtract(finalAmount));
        userRepository.save(user);

        // 8. Send purchase email
        sendPurchaseEmail(user, activeLicenseList);

        return transactionMapper.toDto(transaction);
    }

    @Async
    void sendPurchaseEmail(User user, List<License> licenses) {
        if (!CommonUtil.isEmpty(user.getEmail()) && user.getReminder() != null && user.getReminder().equals(1)) {
            try {
                String emailBody = Constants.EMAIL_BUY_NEW_PROXY;
                String itemStart = "[begin]";
                String itemEnd = "[end]";
                String itemReplace = "HERE_PROXY_INFO";
                String proxyInfo;
                emailBody = emailBody.replace("#NAME", user.getName());
                proxyInfo = emailBody.substring(emailBody.indexOf(itemStart) + itemStart.length());
                proxyInfo = proxyInfo.substring(0, proxyInfo.indexOf(itemEnd));

                int firstPos = emailBody.indexOf(itemStart);
                int lastPos = emailBody.indexOf(itemEnd, firstPos) + itemEnd.length();
                String mainEmail = emailBody.substring(0, firstPos) + itemReplace + emailBody.substring(lastPos);

                List<String> arrProxyInfos = new ArrayList<>();
                for (int i = 0; i < licenses.size(); i++) {
                    License license = licenses.get(i);
                    Proxy httpProxy = license.getHttpProxy();
                    Proxy socketProxy = license.getSockProxy();
                    arrProxyInfos.add((i + 1 + ".") + "" + proxyInfo
                            .replace("#HOST_OF_PROXY", httpProxy.getHost())
                            .replace("#PORT_OF_PROXY", httpProxy.getSharedPort().toString())
                            .replace("#AUTHENTICATE", !CommonUtil.isEmpty(license.getAuthUser()) ? license.getAuthUser() : license.getIpWhitelist())
                            .replace("#START_DATE", license.getStartDate().format(DateTimeFormatter.ofPattern("HH:mm dd-MM-yyyy")))
                            .replace("#END_DATE", license.getExpiredDate().format(DateTimeFormatter.ofPattern("HH:mm dd-MM-yyyy"))));
                }

                String finalMail = mainEmail.replace(itemReplace, String.join("\n", arrProxyInfos));
                EmailDetails emailDetails = EmailDetails.builder()
                        .subject(appConf.getName() + " - Thank You For Your Purchase")
                        .msgBody(finalMail)
                        .recipient(user.getEmail())
                        .build();
                String result = emailService.sendSimpleMail(emailDetails);
                log.info("SEND EMAIL: {}", result);
            } catch (Exception e) {
                log.error("_____ ERROR WHEN SEND MAIL", e);
            }
        }
    }


}
