package com.neoproxy.pro.service.xproxy;

import com.neoproxy.pro.config.AppConf;
import com.neoproxy.pro.config.Constants;
import com.neoproxy.pro.domain.License;
import com.neoproxy.pro.domain.Proxy;
import com.neoproxy.pro.domain.ProxyWan;
import com.neoproxy.pro.domain.User;
import com.neoproxy.pro.dto.ExpressExtendLicenseRequest;
import com.neoproxy.pro.enums.ModemType;
import com.neoproxy.pro.mail.EmailDetails;
import com.neoproxy.pro.mail.EmailService;
import com.neoproxy.pro.service.LicenseService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@Slf4j
public class LicenseManagerService {
    @Autowired
    LicenseService licenseService;
    @Autowired
    AppConf appConf;
    @Autowired
    EmailService emailService;

    @Scheduled(cron = "${app.scheduler.checkLicenseExpiredCron}")
    public void checkLicenseExpired() throws Exception {
        log.info("------ Check license expired -----");
        List<License> expiredLicenseList = licenseService.getExpiredLicenses();
        log.info("------ Total: {}", expiredLicenseList.size());
        if (!expiredLicenseList.isEmpty()) {
            expiredLicenseList.forEach(license -> {
                try {
                    // Check if license has auto renewal enabled
                    if (Boolean.TRUE.equals(license.getAutoRenewal())) {
                        log.info("License {} has auto renewal enabled, attempting express extend", license.getUuid());
                        processAutoRenewal(license);
                    } else {
                        log.info("License {} has auto renewal disabled, updating to expired", license.getUuid());
                        licenseService.updateExpiredLicense(license);
                    }
                } catch (Exception e) {
                    log.error("Error processing license {}: {}", license.getUuid(), e.getMessage(), e);
                    // If auto renewal fails, mark as expired
                    try {
                        licenseService.updateExpiredLicense(license);
                    } catch (Exception ex) {
                        log.error("Failed to update expired license {}: {}", license.getUuid(), ex.getMessage());
                    }
                }
            });
        }
    }

    @Async
    private void processAutoRenewal(License license) {
        try {
            // Check if user has sufficient balance for auto renewal
            User customer = license.getCustomer();
            if (customer == null) {
                log.warn("License {} has no customer, cannot process auto renewal", license.getUuid());
                licenseService.updateExpiredLicense(license);
                return;
            }

            // Create express extend request for this license
            ExpressExtendLicenseRequest request = ExpressExtendLicenseRequest.builder()
                    .uuids(List.of(license.getUuid()))
                    .build();

            // Attempt express extend
            boolean success = licenseService.expressExtendByLicenseIds(request);
            
            if (success) {
                log.info("Auto renewal successful for license {}", license.getUuid());
                sendAutoRenewalSuccessEmail(customer, license);
            } else {
                log.warn("Auto renewal failed for license {}, marking as expired", license.getUuid());
                licenseService.updateExpiredLicense(license);
                sendAutoRenewalFailedEmail(customer, license, "Express extend failed");
            }
            
        } catch (Exception e) {
            log.error("Auto renewal failed for license {}: {}", license.getUuid(), e.getMessage(), e);
            try {
                licenseService.updateExpiredLicense(license);
                sendAutoRenewalFailedEmail(license.getCustomer(), license, e.getMessage());
            } catch (Exception ex) {
                log.error("Failed to handle auto renewal failure for license {}: {}", license.getUuid(), ex.getMessage());
            }
        }
    }

    @Async
    private void sendAutoRenewalSuccessEmail(User customer, License license) {
        try {
            // Calculate renewal amount (package price + VPN fee if applicable)
            BigDecimal renewalAmount = license.getSalePackage().getPrice();
            if (license.getVpnType() != null && license.getSalePackage().getVpnFee() != null) {
                renewalAmount = renewalAmount.add(license.getSalePackage().getVpnFee());
            }

            String subject = "Your Proxy License Has Been Successfully Renewed";
            String body = String.format(
                "Dear %s,\n\n" +
                "We're pleased to inform you that your proxy license has been successfully renewed automatically.\n\n" +
                "License Details:\n\n" +
                "Plan: %s\n" +
                "Location: %s\n" +
                "License ID: %s\n" +
                "New Expiration Date: %s\n" +
                "Renewal Amount: $%.2f\n" +
                "Current Balance: $%.2f\n\n" +
                "No further action is required from your side. You can continue to enjoy uninterrupted service.\n\n" +
                "If you have any questions or need assistance, feel free to contact our support team %s\n\n" +
                "Thank you for choosing our service!\n\n" +
                "Best regards,\n" +
                "AAAProxy.IO",
                customer.getName(),
                license.getSalePackage().getName(),
                license.getLocation() != null ? license.getLocation() : "N/A",
                license.getUuid(),
                license.getExpiredDate().format(DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm")),
                renewalAmount,
                customer.getBalance(),
                appConf.getTelegram() != null ? appConf.getTelegram() : "<EMAIL>"
            );

            EmailDetails emailDetails = EmailDetails.builder()
                    .subject(subject)
                    .msgBody(body)
                    .recipient(customer.getEmail())
                    .build();
            
            String result = emailService.sendSimpleMail(emailDetails);
            log.info("Auto renewal success email sent: {}", result);
        } catch (Exception e) {
            log.error("Failed to send auto renewal success email: {}", e.getMessage());
        }
    }

    @Async
    private void sendAutoRenewalFailedEmail(User customer, License license, String reason) {
        try {
            // Calculate renewal amount required (package price + VPN fee if applicable)
            BigDecimal renewalAmountRequired = license.getSalePackage().getPrice();
            if (license.getVpnType() != null && license.getSalePackage().getVpnFee() != null) {
                renewalAmountRequired = renewalAmountRequired.add(license.getSalePackage().getVpnFee());
            }

            // Calculate amount to top-up
            BigDecimal currentBalance = customer.getBalance();
            BigDecimal amountToTopUp = renewalAmountRequired.subtract(currentBalance);
            if (amountToTopUp.compareTo(BigDecimal.ZERO) < 0) {
                amountToTopUp = BigDecimal.ZERO;
            }

            String subject = "Action Required: License Renewal Failed — License Expired";
            String body = String.format(
                "Dear %s,\n\n" +
                "We attempted to automatically renew your proxy license (License ID: %s, Plan: %s), but the renewal was unsuccessful due to insufficient account balance.\n\n" +
                "As a result, this license has now expired.\n\n" +
                "Details:\n\n" +
                "Renewal Amount Required: $%.2f\n" +
                "Current Balance: $%.2f\n" +
                "Amount to Top-Up: $%.2f\n\n" +
                "To continue using this service, please top up your account balance and repurchase the license.\n" +
                "If you have any questions or need assistance, feel free to contact our support team %s\n\n" +
                "Thank you for using our service!\n\n" +
                "Best regards,\n" +
                "AAAProxy.IO",
                customer.getName(),
                license.getUuid(),
                license.getSalePackage().getName(),
                renewalAmountRequired,
                currentBalance,
                amountToTopUp,
                appConf.getTelegram() != null ? appConf.getTelegram() : "<EMAIL>"
            );

            EmailDetails emailDetails = EmailDetails.builder()
                    .subject(subject)
                    .msgBody(body)
                    .recipient(customer.getEmail())
                    .build();
            
            String result = emailService.sendSimpleMail(emailDetails);
            log.info("Auto renewal failed email sent: {}", result);
        } catch (Exception e) {
            log.error("Failed to send auto renewal failed email: {}", e.getMessage());
        }
    }

    @Scheduled(cron = "${app.scheduler.alertLicenseExpiredCron}")
    public void alertLicenseExpired() throws Exception {
        log.info("------ Alert license expired -----");
        List<User> users = licenseService.getExpiredLicensesNextDay();
        log.info("------ Total: {}", users.size());
        if (!users.isEmpty()) {
            users.forEach(user -> {
                sendAlertExpiredEmail(user, user.getLicenses());
            });
        }
    }

    @Async
    void sendAlertExpiredEmail(User user, List<License> licenses) {
        if (user.getReminder().equals(1)) {
            try {
                String emailBody = Constants.EMAIL_ALERT_EXPIRED;
                String itemStart = "[begin]";
                String itemEnd = "[end]";
                String itemReplace = "HERE_PROXY_INFO";
                String proxyInfo;
                emailBody = emailBody.replace("#NAME", user.getName());
                proxyInfo = emailBody.substring(emailBody.indexOf(itemStart) + itemStart.length());
                proxyInfo = proxyInfo.substring(0, proxyInfo.indexOf(itemEnd));

                int firstPos = emailBody.indexOf(itemStart);
                int lastPos = emailBody.indexOf(itemEnd, firstPos) + itemEnd.length();
                String mainEmail = emailBody.substring(0, firstPos) + itemReplace + emailBody.substring(lastPos);

                List<String> arrProxyInfos = new ArrayList<>();
                for (int i = 0; i < licenses.size(); i++) {
                    License license = licenses.get(i);
                    Proxy httpProxy = license.getHttpProxy();
                    Proxy socketProxy = license.getSockProxy();
                    arrProxyInfos.add((i + 1 + ".") + "" + proxyInfo
                            .replace("#HOST_OF_PROXY", httpProxy.getHost())
                            .replace("#PORT_OF_PROXY", httpProxy.getSharedPort().toString())
                            .replace("#START_DATE", license.getStartDate().format(DateTimeFormatter.ofPattern("HH:mm dd-MM-yyyy")))
                            .replace("#END_DATE", license.getExpiredDate().format(DateTimeFormatter.ofPattern("HH:mm dd-MM-yyyy"))));
                }

                String finalMail = mainEmail.replace(itemReplace, String.join("\n", arrProxyInfos));
                EmailDetails emailDetails = EmailDetails.builder()
                        .subject(appConf.getName() + " - Proxy expire next day")
                        .msgBody(finalMail)
                        .recipient(user.getEmail())
                        .build();
                String result = emailService.sendSimpleMail(emailDetails);
                log.info("SEND EMAIL: {}", result);
            } catch (Exception e) {
                log.error("_____ ERROR WHEN SEND MAIL", e);
            }
        }
    }
}
