package com.neoproxy.pro.domain;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.neoproxy.pro.enums.PromotionDiscountType;
import com.neoproxy.pro.enums.PromotionStatus;
import com.neoproxy.pro.enums.PromotionConditionType;
import lombok.*;
import lombok.experimental.FieldDefaults;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedDate;
import org.springframework.data.jpa.domain.support.AuditingEntityListener;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.security.Principal;
import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = "promotions")
@NoArgsConstructor
@AllArgsConstructor
@Builder(toBuilder = true)
@FieldDefaults(level = AccessLevel.PRIVATE)
@EntityListeners(AuditingEntityListener.class)
public class Promotion implements Serializable, Principal {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    Long id;

    @Column(name = "uuid", updatable = false)
    UUID uuid;

    @Column(name = "code")
    String code;

    @Column(name = "description")
    String description;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    PromotionStatus status;

    @Enumerated(EnumType.STRING)
    @Column(name = "discount_type")
    PromotionDiscountType discountType;

    @Column(name = "discount_value")
    BigDecimal discountValue;

    @Column(name = "min_amount")
    BigDecimal minAmount;

    @Column(name = "max_discount")
    BigDecimal maxDiscount;

    @Enumerated(EnumType.STRING)
    @Column(name = "condition_type")
    PromotionConditionType conditionType;

    @Column(name = "threshold_value")
    Integer thresholdValue;

    @Column(name = "start_time")
    LocalDateTime startTime;

    @Column(name = "end_time")
    LocalDateTime endTime;

    @Column(name = "created_at")
    @CreatedDate
    LocalDateTime createdAt;

    @Column(name = "updated_at")
    @LastModifiedDate
    LocalDateTime updatedAt;

    @PrePersist
    protected void onCreate() {
        setUuid(UUID.randomUUID());
    }

    @Override
    @JsonIgnore
    public String getName() {
        return "";
    }
}
