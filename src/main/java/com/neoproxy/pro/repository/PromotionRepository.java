package com.neoproxy.pro.repository;

import com.neoproxy.pro.domain.Promotion;
import com.neoproxy.pro.enums.PromotionConditionType;
import com.neoproxy.pro.enums.PromotionStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface PromotionRepository extends JpaRepository<Promotion, Long>, JpaSpecificationExecutor<Promotion> {
    Promotion findByUuid(UUID uuid);

    Optional<Promotion> findByCode(String code);

    @Query(
            """
                    SELECT p
                    FROM Promotion p
                    WHERE p.conditionType = :conditionType
                      and p.status = :promotionStatus
                    """)
    List<Promotion> getPromotions(PromotionStatus promotionStatus, PromotionConditionType conditionType);
}
