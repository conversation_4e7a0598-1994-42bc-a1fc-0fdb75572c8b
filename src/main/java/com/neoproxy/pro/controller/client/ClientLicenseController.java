package com.neoproxy.pro.controller.client;

import com.neoproxy.pro.controller.BaseController;
import com.neoproxy.pro.dto.*;
import com.neoproxy.pro.service.LicenseService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@RestController
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequestMapping("/v1/client/licenses")
@Slf4j
public class ClientLicenseController extends BaseController {
    LicenseService licenseService;

    @PostMapping("/list")
    public ResponseEntity<MessageResponse> getClientLicenses(@RequestBody LicenseQueryRequest licenseQueryRequest) {
        try {
            return createSuccessResponse(licenseService.getLicenses(licenseQueryRequest));
        } catch (Exception e) {
            log.error("Create ", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }
    }

    @PostMapping("/extend")
    public ResponseEntity<MessageResponse> renewIp(@RequestBody ExtendLicenseRequest request) {
        try {
            return createSuccessResponse(licenseService.extendByLicenseIds(request));
        } catch (Exception e) {
            log.error("Create ", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }
    }

    // Thêm endpoint express extend
    @PostMapping("/express-extend")
    public ResponseEntity<MessageResponse> expressExtend(@RequestBody ExpressExtendLicenseRequest request) {
        try {
            return createSuccessResponse(licenseService.expressExtendByLicenseIds(request));
        } catch (Exception e) {
            log.error("Express extend error: ", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }
    }

    @PostMapping("/toggle-auto-renewal")
    public ResponseEntity<MessageResponse> toggleAutoRenewal(@RequestBody ToggleAutoRenewalRequest request) {
        try {
            return createSuccessResponse(licenseService.toggleAutoRenewal(request));
        } catch (Exception e) {
            log.error("Toggle auto renewal error: ", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }
    }

    @PostMapping("/purchase-vpn")
    public ResponseEntity<MessageResponse> purchaseVpn(@RequestBody PurchaseVpnRequest request) {
        try {
            return createSuccessResponse(licenseService.purchaseVpn(request));
        } catch (Exception e) {
            log.error("Purchase VPN error: ", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }
    }

    @PostMapping("/cancel-vpn")
    public ResponseEntity<MessageResponse> cancelVpn(@RequestBody CancelVpnRequest request) {
        try {
            return createSuccessResponse(licenseService.cancelVpn(request));
        } catch (Exception e) {
            log.error("Cancel VPN error: ", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }
    }

    @PostMapping("/change-rotation-time")
    public ResponseEntity<MessageResponse> changeRotationTime(@RequestBody ChangeRotationTimeRequest request) {
        try {
            return createSuccessResponse(licenseService.changeRotationTime(request));
        } catch (Exception e) {
            log.error("Update", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }
    }

    @PostMapping("/update-tcp-os")
    public ResponseEntity<MessageResponse> updateTcpOS(@RequestBody UpdateTcpOSRequest request) {
        try {
            return createSuccessResponse(licenseService.updateTcpOSForLicenses(request));
        } catch (Exception e) {
            log.error("Update TCP OS error: ", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }
    }

    @RequestMapping("/excel")
    public void excel(HttpServletResponse servletResponse, @RequestParam("customer") String customer) throws IOException {
        servletResponse.setContentType("text/csv; charset=UTF-8");
        servletResponse.setCharacterEncoding( "UTF-8" );
        servletResponse.addHeader("Content-Disposition","attachment; filename=\"licenses.csv\"");
        licenseService.writeLicenseToCsv(customer, servletResponse.getWriter());
    }
}
