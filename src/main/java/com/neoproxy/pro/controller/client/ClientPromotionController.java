package com.neoproxy.pro.controller.client;

import com.neoproxy.pro.controller.BaseController;
import com.neoproxy.pro.dto.MessageResponse;
import com.neoproxy.pro.dto.PromotionQueryRequest;
import com.neoproxy.pro.service.PackageService;
import com.neoproxy.pro.service.PromotionService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;

@RestController
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequestMapping("/v1/client/promotions")
@Slf4j
public class ClientPromotionController extends BaseController {
    PromotionService promotionService;

    PackageService packageService;

    @GetMapping("/redeem-code")
    public ResponseEntity<MessageResponse> redeemCode(
            @RequestParam("promotionCode") String promotionCode, @RequestParam("totalAmount") BigDecimal totalAmount) {
        try {
            return createSuccessResponse(promotionService.redeemCode(promotionCode, totalAmount));
        } catch (Exception e) {
            log.error("Create ", e);
            return createFailResponse(e.getMessage(), null, HttpStatus.OK);
        }

    }

    @PostMapping("/quantity")
    public ResponseEntity<MessageResponse> getPromotionQuantity(@RequestBody PromotionQueryRequest packageQueryRequest) {
        return createSuccessResponse(promotionService.getPromotionQuantity(packageQueryRequest));
    }

    @GetMapping("/discount")
    public ResponseEntity<MessageResponse> getDiscountByPromotion(@RequestParam("id") String uuid, @RequestParam("totalAmount") BigDecimal totalAmount) {
        return createSuccessResponse(promotionService.getDiscountByPromotion(uuid, totalAmount));
    }
}
