package com.neoproxy.pro.controller.client;

import com.neoproxy.pro.controller.BaseController;
import com.neoproxy.pro.dto.MessageResponse;
import com.neoproxy.pro.dto.PackageQueryRequest;
import com.neoproxy.pro.service.PackageService;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequestMapping("/v1/client/packages")
@Slf4j
public class ClientPackageController extends BaseController {
    PackageService packageService;

    @PostMapping("/list")
    public ResponseEntity<MessageResponse> getPackages(@RequestBody PackageQueryRequest packageQueryRequest) {
        return createSuccessResponse(packageService.getClientPackages(packageQueryRequest));
    }
}
