package com.neoproxy.pro.enums;

public enum ErrorCode {
    // Refresh token not found
    REFRESH_TOKEN_NOT_FOUND,
    // Unauthorized
    UNAUTHORIZED,
    // Forbidden
    FORBIDDEN,
    USER_NOT_FOUND,
    USER_EXISTED,
    <PERSON>ONG_PASSWORD,
    INSUFFICIENT_BALANCE,
    ENTITY_NOT_FOUND,
    EMAIL_ADDRESS_EXISTED,
    BAD_DATA,
    ERROR_CONFIGURATION,
    MODEM_ERROR_CONNECT,
    MODEM_NOT_ACTIVE,
    MODEM_HAVE_EXISTED,
    MODEM_PENDING,
    PROXY_NOT_ENOUGH,
    PROXY_ERROR_CONNECT,
    PROXY_ACTIVE_NOT_FOUND,
    PROXY_TOO_FAST_TO_CHANGE_IP,
    NOT_HAVE_PERMISSION,
    LICENSE_ACTIVE_NOT_FOUND,
    LOCATION_NOT_FOUND,
    // NowPayment
    PAYMENT_SERVER_ERROR,
    WRONG_ORDER_AMOUNT
}
