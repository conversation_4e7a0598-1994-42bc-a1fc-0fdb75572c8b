package com.neoproxy.pro.dto;

import com.neoproxy.pro.enums.PackageStatus;
import com.neoproxy.pro.enums.PackageType;
import com.neoproxy.pro.enums.PackageUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PackageRequest {
    String uuid;
    String name;
    PackageUnit packageUnit;
    Integer duration;
    BigDecimal price;
    BigDecimal vpnFee;
    BigDecimal locationFee;
    String location;
    String isp;
    PackageStatus status;
    PackageType type;
    Integer allowChangeIp;
    Integer allowChangeLocation;
    Integer allowChangeISP;
    Long minTimeChangeIp;
    Integer seq;
}
