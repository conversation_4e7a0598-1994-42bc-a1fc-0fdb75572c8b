package com.neoproxy.pro.dto;

import com.neoproxy.pro.enums.PromotionConditionType;
import com.neoproxy.pro.enums.PromotionDiscountType;
import com.neoproxy.pro.enums.PromotionStatus;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PromotionDto {
    UUID uuid;
    String code;
    String description;
    PromotionStatus status;
    PromotionDiscountType discountType;
    BigDecimal discountValue;
    BigDecimal minAmount;
    BigDecimal maxDiscount;
    LocalDateTime startTime;
    LocalDateTime endTime;
    PromotionConditionType conditionType;
    Integer thresholdValue;
}
