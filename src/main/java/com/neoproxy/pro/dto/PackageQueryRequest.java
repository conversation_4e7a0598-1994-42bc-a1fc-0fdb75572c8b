package com.neoproxy.pro.dto;

import com.neoproxy.pro.enums.PackageUnit;
import lombok.Data;

@Data
public class PackageQueryRequest extends PageableRequest {
    private static String NAME = "name";
    private static String STATUS = "status";
    private static String LOCATION = "location";
    private static String PKG_UNIT = "packageUnit";

    public String getName() {
        return getValueFilter(NAME);
    }

    public String getLocation() {
        return getValueFilter(LOCATION);
    }

    public String getStatus() {
        return getValueFilter(STATUS);
    }

    public String getPkgUnit() {
        return getValueFilter(PKG_UNIT);
    }
}
