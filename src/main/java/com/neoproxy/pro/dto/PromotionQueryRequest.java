package com.neoproxy.pro.dto;

import lombok.Data;

@Data
public class PromotionQueryRequest extends PageableRequest {
    private static String CODE = "code";
    private static String STATUS = "status";
    private static String CONDITION_TYPE = "conditionType";

    public String getCode() {
        return getValueFilter(CODE);
    }

    public String getStatus() {
        return getValueFilter(STATUS);
    }

    public String getConditionType() {
        return getValueFilter(CONDITION_TYPE);
    }
}
