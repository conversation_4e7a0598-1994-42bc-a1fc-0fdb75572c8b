package com.neoproxy.pro.dto;

import com.neoproxy.pro.enums.VpnType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.UUID;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseVpnRequest {
    List<UUID> licenseUuids; // Danh sách UUID của licenses cần mua VPN
    VpnType vpnType; // Loại VPN: OPEN_VPN hoặc WIRE_GUARD
}