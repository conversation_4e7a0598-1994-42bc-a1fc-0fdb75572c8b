package com.neoproxy.pro.dto;

import com.neoproxy.pro.enums.PaymentMethodType;
import com.neoproxy.pro.enums.VpnType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderProxyV2Request {
    UUID packageUuid;
    Integer quantity;
    Integer time;
    String whiteListIps;
    String username;
    String promotionCode;
    VpnType vpnType;
    PaymentMethodType paymentMethod;

    // For crypto
    String payCurrency;
    String priceCurrency;

    // For Promotion
    String durationPromotionId;
    String quantityPromotionId;

    // Double Check With Frontend
    BigDecimal totalAmount;
}
