package com.neoproxy.pro.dto;

import com.neoproxy.pro.enums.PromotionConditionType;
import com.neoproxy.pro.enums.PromotionDiscountType;
import com.neoproxy.pro.enums.PromotionStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PromotionRequest {
    String uuid;
    String code;
    String description;
    PromotionStatus status;
    PromotionDiscountType discountType;
    BigDecimal discountValue;
    BigDecimal minAmount;
    BigDecimal maxDiscount;
    LocalDateTime startTime;
    LocalDateTime endTime;
    PromotionConditionType conditionType;
    Integer thresholdValue;
}
