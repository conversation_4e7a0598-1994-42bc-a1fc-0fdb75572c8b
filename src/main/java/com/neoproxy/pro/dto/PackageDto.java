package com.neoproxy.pro.dto;

import com.neoproxy.pro.enums.PackageStatus;
import com.neoproxy.pro.enums.PackageType;
import com.neoproxy.pro.enums.PackageUnit;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.FieldDefaults;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

@Data
@NoArgsConstructor
@AllArgsConstructor
@FieldDefaults(level = AccessLevel.PRIVATE)
public class PackageDto {
    UUID uuid;
    String name;
    PackageUnit packageUnit;
    Integer duration;
    BigDecimal price;
    PackageStatus status;
    PackageType type;
    Integer allowChangeIp;
    Integer allowChangeLocation;
    Integer allowChangeISP;
    Integer minTimeChangeIp;
    String location;
    String isp;
    Integer seq;
    BigDecimal vpnFee;
    BigDecimal locationFee;

    Long speedTestUploadBytes;
    Long speedTestDownloadBytes;
    String speedTestTime;

    String promotionId;
    String promotionDes;
    BigDecimal promotionPercent;
}
